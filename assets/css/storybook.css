/* See the Tailwind configuration guide for advanced usage
   https://tailwindcss.com/docs/configuration */

@import "tailwindcss" source(none);
@source "../css";
@source "../js";
@source "../../lib/l3rn_dev_web";

/* A Tailwind plugin that makes "hero-#{ICON}" classes available.
   The heroicons installation itself is managed by your mix.exs */
@plugin "../vendor/heroicons";

/* daisyUI Tailwind Plugin. You can update this file by fetching the latest version with:
   curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui.js
   Make sure to look at the daisyUI changelog: https://daisyui.com/docs/changelog/ */
@plugin "../vendor/daisyui" {
    themes: false;
}

/* daisyUI theme plugin. You can update this file by fetching the latest version with:
  curl -sLO https://github.com/saadeghi/daisyui/releases/latest/download/daisyui-theme.js
  We ship with two themes, a light one inspired on Phoenix colors and a dark one inspired
  on Elixir colors. Build your own at: https://daisyui.com/theme-generator/ */
@plugin "../vendor/daisyui-theme" {
    name: "dark";
    default: false;
    prefersdark: true;
    color-scheme: "dark";
    --color-base-100: oklch(30.33% 0.016 252.42);
    --color-base-200: oklch(25.26% 0.014 253.1);
    --color-base-300: oklch(20.15% 0.012 254.09);
    --color-base-content: oklch(97.807% 0.029 256.847);
    --color-primary: oklch(58% 0.233 277.117);
    --color-primary-content: oklch(96% 0.018 272.314);
    --color-secondary: oklch(58% 0.233 277.117);
    --color-secondary-content: oklch(96% 0.018 272.314);
    --color-accent: oklch(60% 0.25 292.717);
    --color-accent-content: oklch(96% 0.016 293.756);
    --color-neutral: oklch(37% 0.044 257.287);
    --color-neutral-content: oklch(98% 0.003 247.858);
    --color-info: oklch(58% 0.158 241.966);
    --color-info-content: oklch(97% 0.013 236.62);
    --color-success: oklch(60% 0.118 184.704);
    --color-success-content: oklch(98% 0.014 180.72);
    --color-warning: oklch(66% 0.179 58.318);
    --color-warning-content: oklch(98% 0.022 95.277);
    --color-error: oklch(58% 0.253 17.585);
    --color-error-content: oklch(96% 0.015 12.422);
    --radius-selector: 0.25rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.21875rem;
    --size-field: 0.21875rem;
    --border: 1.5px;
    --depth: 1;
    --noise: 0;
}

@plugin "../vendor/daisyui-theme" {
    name: "light";
    default: true;
    prefersdark: false;
    color-scheme: "light";
    --color-base-100: oklch(98% 0 0);
    --color-base-200: oklch(96% 0.001 286.375);
    --color-base-300: oklch(92% 0.004 286.32);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(70% 0.213 47.604);
    --color-primary-content: oklch(98% 0.016 73.684);
    --color-secondary: oklch(55% 0.027 264.364);
    --color-secondary-content: oklch(98% 0.002 247.839);
    --color-accent: oklch(0% 0 0);
    --color-accent-content: oklch(100% 0 0);
    --color-neutral: oklch(44% 0.017 285.786);
    --color-neutral-content: oklch(98% 0 0);
    --color-info: oklch(62% 0.214 259.815);
    --color-info-content: oklch(97% 0.014 254.604);
    --color-success: oklch(70% 0.14 182.503);
    --color-success-content: oklch(98% 0.014 180.72);
    --color-warning: oklch(66% 0.179 58.318);
    --color-warning-content: oklch(98% 0.022 95.277);
    --color-error: oklch(58% 0.253 17.585);
    --color-error-content: oklch(96% 0.015 12.422);
    --radius-selector: 0.25rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.21875rem;
    --size-field: 0.21875rem;
    --border: 1.5px;
    --depth: 1;
    --noise: 0;
}

/* Add variants based on LiveView classes */
@custom-variant phx-click-loading (.phx-click-loading&, .phx-click-loading &);
@custom-variant phx-submit-loading (.phx-submit-loading&, .phx-submit-loading &);
@custom-variant phx-change-loading (.phx-change-loading&, .phx-change-loading &);

/* Make LiveView wrapper divs transparent for layout */
[data-phx-session] {
    display: contents;
}

@import "../../deps/live_monaco_editor/priv/static/live_monaco_editor.min.css";
/* This file is for your main application CSS */

.liveview-container {
    @apply h-screen bg-gray-50;
}

/* Customize highlight.js to work better with our line component */
pre {
    @apply bg-transparent m-0 p-0;
}

code {
    @apply bg-transparent;
}

.hljs {
    @apply bg-transparent p-0 m-0;
}

/* Override some highlight.js GitHub theme styles for better line component integration */
.hljs-comment,
.hljs-quote {
    @apply text-gray-500;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
    @apply text-blue-600;
}

.hljs-string,
.hljs-doctag {
    @apply text-green-600;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
    @apply text-purple-600;
}

.hljs-type,
.hljs-class .hljs-title {
    @apply text-yellow-600;
}

/* Make sure code doesn't change height of line */
.line-wrapper pre,
.line-wrapper code {
    @apply inline m-0 p-0;
}

@layer utilities {
    * {
        font-family: system-ui;
    }
}
