import Config

# Configure your database
config :l3rn_dev, L3rn<PERSON>ev.Repo,
  username: "fullstack",
  password: "pfullstackw",
  hostname: "localhost",
  database: "l3rn_dev_dev",
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we can use it
# to bundle .js and .css sources.
config :l3rn_dev, L3rnDevWeb.Endpoint,
  # Bind to 0.0.0.0 to expose the server to the docker host machine.
  # This makes make the service accessible from any network interface.
  # Change to `ip: {127, 0, 0, 1}` to allow access only from the server machine.
  http: [ip: {0, 0, 0, 0}, port: String.to_integer(System.get_env("PORT") || "4000")],
  check_origin: false,
  code_reloader: true,
  reloadable_compilers: [:gettext, :elixir, :app, :phoenix_live_view, :tailwind],
  debug_errors: true,
  secret_key_base: "emhJyQmZ7p2ggzB5wGEdXB+nuFCtNEI/90TuJbfphjd9m4dqm01K9yKcZ60bu/6+",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:l3rn_dev, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:l3rn_dev, ~w(--watch)]},
    storybook_tailwind: {Tailwind, :install_and_run, [:storybook, ~w(--watch)]}
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Watch static and templates for browser reloading.
config :l3rn_dev, L3rnDevWeb.Endpoint,
  live_reload: [
    web_console_logger: true,
    patterns: [
      ~r"priv/static/(?!uploads/).*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/l3rn_dev_web/(controllers|live|components)/.*(ex|heex)$",
      ~r"storybook/.*(exs)$"
    ]
  ]

config :l3rn_dev, dotenv: true, token_signing_secret: "+BdW2OjuhaPF8Hf2JxjqLs10pILgXIrH"

# Enable dev routes for dashboard and mailbox
config :l3rn_dev, dev_routes: true

# Do not include metadata nor timestamps in development logs
config :logger, :default_formatter, format: "[$level] $message\n"

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime

# Include HEEx debug annotations as HTML comments in rendered markup
config :phoenix_live_view,
  # Include HEEx debug annotations as HTML comments in rendered markup.
  # Changing this configuration will require mix clean and a full recompile.
  debug_heex_annotations: true,
  debug_tags_location: true,
  # Enable helpful, but potentially expensive runtime checks
  enable_expensive_runtime_checks: true

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false

config :ueberauth, Ueberauth.Strategy.Github.OAuth, ignores_csrf_attack: true

config :live_debugger, :debug_button?, false
