import Config
config :l3rn_dev, token_signing_secret: "600FHxeENKrlExhu+YkfWPeWdmHw5jbM"
config :l3rn_dev, <PERSON><PERSON>, testing: :manual

# Only in tests, remove the complexity from the password hashing algorithm
config :bcrypt_elixir, :log_rounds, 1

# Configure your database
#
# The MIX_TEST_PARTITION environment variable can be used
# to provide built-in test partitioning in CI environment.
# Run `mix help test` for more information.
config :l3rn_dev, L3rnDev.Repo,
  username: System.get_env("POSTGRES_USER", "fullstack"),
  password: System.get_env("POSTGRES_PASSWORD", "pfullstackw"),
  hostname: System.get_env("POSTGRES_HOST", "localhost"),
  port: System.get_env("POSTGRES_PORT", "5432"),
  database: "l3rn_dev_test#{System.get_env("MIX_TEST_PARTITION")}",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: System.schedulers_online() * 2

# We don't run a server during test. If one is required,
# you can enable the server option below.
config :l3rn_dev, L3rnDevWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4002],
  secret_key_base: "5KmmfipmmNn4CC9Bhn/X/0uU9vyJ0a+BT1G9k4FJeQPjBwXIr/BThwSWm3AABY0u",
  server: false

# In test we don't send emails
config :l3rn_dev, L3rnDev.Mailer, adapter: Swoosh.Adapters.Test

# Disable swoosh api client as it is only required for production adapters
config :swoosh, :api_client, false

# Print only warnings and errors during test
config :logger, level: :warning

# Initialize plugs at runtime for faster test compilation
config :phoenix, :plug_init_mode, :runtime

# Enable helpful, but potentially expensive runtime checks
config :phoenix_live_view,
  enable_expensive_runtime_checks: true
