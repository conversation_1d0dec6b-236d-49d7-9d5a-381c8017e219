defmodule L3rnDevWeb.ConnCase do
  @moduledoc """
  This module defines the test case to be used by
  tests that require setting up a connection.

  Such tests rely on `Phoenix.ConnTest` and also
  import other functionality to make it easier
  to build common data structures and query the data layer.

  Finally, if the test case interacts with the database,
  we enable the SQL sandbox, so changes done to the database
  are reverted at the end of every test. If you are using
  PostgreSQL, you can even run database tests asynchronously
  by setting `use L3rnDevWeb.ConnCase, async: true`, although
  this option is not recommended for other databases.
  """

  use ExUnit.CaseTemplate
  alias AshAuthentication.Strategy.MagicLink

  using do
    quote do
      use L3rnDevWeb, :verified_routes

      # Import conveniences for testing with connections
      import Plug.Conn
      import Phoenix.ConnTest
      import L3rnDevWeb.ConnCase

      # The default endpoint for testing
      @endpoint L3rnDevWeb.Endpoint
    end
  end

  setup tags do
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(L3rnDev.Repo)

    unless tags[:async] do
      Ecto.Adapters.SQL.Sandbox.mode(L3rnDev.Repo, :manual)
    end

    {:ok, conn: Phoenix.ConnTest.build_conn()}
  end

  @doc """
  Setup helper that registers and logs in a user.

  Following the pattern from ash_authentication documentation:
  https://hexdocs.pm/ash_authentication/testing.html#testing-authenticated-liveviews

  It returns an updated map with the :conn updated to have the user
  in the session and the :user key with the created user.
  """
  def register_and_log_in_user(%{conn: conn} = context) do
    email = "<EMAIL>"

    user =
      Ash.Seed.seed!(L3rnDev.Accounts.User, %{
        email: email
      })

    strategy = AshAuthentication.Info.strategy!(L3rnDev.Accounts.User, :magic_link)
    identity = user.email
    context_opts = Ash.Context.to_opts(context)

    {:ok, token} = MagicLink.request_token_for_identity(strategy, identity, context_opts, context)

    {:ok, authenticated_user} =
      AshAuthentication.Strategy.action(strategy, :sign_in, %{
        token: token
      })

    new_conn =
      conn
      |> Phoenix.ConnTest.init_test_session(%{})
      |> AshAuthentication.Plug.Helpers.store_in_session(authenticated_user)

    %{
      context
      | conn: %{new_conn | assigns: Map.put(new_conn.assigns, :current_user, authenticated_user)}
    }
  end

  @doc """
  Alternative helper that goes through the full magic link flow for integration tests.
  """
  def register_and_log_in_user_with_magic_link(%{conn: conn} = context) do
    email = "<EMAIL>"

    user =
      Ash.Seed.seed!(L3rnDev.Accounts.User, %{
        email: email
      })

    strategy = AshAuthentication.Info.strategy!(L3rnDev.Accounts.User, :magic_link)
    identity = user.email
    context_opts = Ash.Context.to_opts(context)

    {:ok, token} = MagicLink.request_token_for_identity(strategy, identity, context_opts, context)

    {:ok, authenticated_user} =
      AshAuthentication.Strategy.action(strategy, :sign_in, %{
        token: token
      })

    new_conn =
      conn
      |> Phoenix.ConnTest.init_test_session(%{})
      |> AshAuthentication.Plug.Helpers.store_in_session(authenticated_user)

    %{
      context
      | conn: %{new_conn | assigns: Map.put(new_conn.assigns, :current_user, authenticated_user)}
    }
  end
end
