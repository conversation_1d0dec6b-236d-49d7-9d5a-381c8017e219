defmodule L3rnDev.AccountsTest do
  use L3rnDevWeb.ConnCase

  alias L3rnDev.Accounts.User

  describe "users" do
    test "registers a user from github" do
      attrs = %{
        github_uid: "12345",
        email: "<EMAIL>",
        avatar: "http://example.com/avatar.png",
        github_nickname: "gh_user",
        github_token: "gh_token",
        github_token_expires_at: NaiveDateTime.utc_now(),
        github_refresh_token: "gh_refresh",
        github_refresh_token_expires_at: NaiveDateTime.utc_now()
      }

      user = register_from_github(attrs)

      assert to_string(user.email) == "<EMAIL>"
      assert user.github_uid == "12345"
    end
  end

  defp register_from_github(attrs) do
    User
    |> Ash.Changeset.for_create(:register_from_github, attrs)
    |> Ash.create!()
  end
end
