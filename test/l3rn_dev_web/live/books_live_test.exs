defmodule L3rnDevWeb.BooksLiveTest do
  use L3rnDevWeb.ConnCase

  import Phoenix.LiveViewTest

  alias L3rnDev.Books.Book

  describe "Books index page" do
    test "requires authentication", %{conn: conn} do
      assert {:error, {:redirect, %{to: "/sign-in"}}} = live(conn, ~p"/books")
    end

    test "lists all books for the current user", context do
      %{conn: conn} = register_and_log_in_user(context)

      Ash.Changeset.for_create(Book, :create, %{title: "Test Book"},
        actor: conn.assigns.current_user
      )
      |> Ash.create!()

      {:ok, _lv, html} = live(conn, ~p"/books")

      assert html =~ "Books"
      assert html =~ "Test Book"
    end

    test "shows 'No books yet' message if no books exist", context do
      %{conn: conn} = register_and_log_in_user(context)
      {:ok, _lv, html} = live(conn, ~p"/books")

      assert html =~ "No books yet"
      assert html =~ "Get started by creating a new book."
    end
  end

  describe "Book creation" do
    test "can navigate to new book page", context do
      %{conn: conn} = register_and_log_in_user(context)
      {:ok, _lv, html} = live(conn, ~p"/books/new")

      assert html =~ "New Book"
      assert html =~ "Title"
    end

    test "can create a new book", context do
      %{conn: conn} = register_and_log_in_user(context)
      {:ok, lv, _html} = live(conn, ~p"/books/new")

      {:error, {:live_redirect, %{to: to}}} =
        lv |> form("#book-form", form: %{title: "New Awesome Book"}) |> render_submit()

      assert_redirected(lv, to)

      {:ok, _lv, html} = live(conn, to)
      assert html =~ "New Awesome Book"
    end
  end
end
