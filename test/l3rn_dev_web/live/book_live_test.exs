defmodule L3rnDevWeb.BookLiveTest do
  use L3rnDevWeb.ConnCase

  import Phoenix.LiveViewTest

  alias L3rnDev.Books.Book

  describe "Book viewing" do
    test "can view a book", context do
      %{conn: conn} = register_and_log_in_user(context)

      book =
        Ash.Changeset.for_create(Book, :create, %{title: "Viewable Book"},
          actor: conn.assigns.current_user
        )
        |> Ash.create!()

      {:ok, _lv, html} = live(conn, ~p"/books/#{book.id}")

      assert html =~ book.title
    end
  end
end
