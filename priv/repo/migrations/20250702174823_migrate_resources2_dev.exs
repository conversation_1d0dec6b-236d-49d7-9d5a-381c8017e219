defmodule L3rnDev.Repo.Migrations.MigrateResources2 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:sections) do
      add :title, :text, null: false
    end
  end

  def down do
    alter table(:sections) do
      remove :title
    end
  end
end
