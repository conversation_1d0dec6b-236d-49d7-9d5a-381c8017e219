defmodule L3rnDev.Repo.Migrations.MigrateResources1 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:users, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :email, :citext, null: false
      add :github_uid, :text
      add :avatar, :text
      add :github_nickname, :text
      add :github_token, :text
      add :github_token_expires_at, :naive_datetime
      add :github_refresh_token, :text
      add :github_refresh_token_expires_at, :naive_datetime
    end

    create unique_index(:users, [:email], name: "users_unique_email_index")

    create table(:tokens, primary_key: false) do
      add :jti, :text, null: false, primary_key: true
      add :subject, :text, null: false
      add :expires_at, :utc_datetime, null: false
      add :purpose, :text, null: false
      add :extra_data, :map

      add :created_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:source_snippets, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :user_id,
          references(:users,
            column: :id,
            name: "source_snippets_user_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :name, :text, null: false
      add :visibility, :text, null: false, default: "private"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create unique_index(:source_snippets, [:name, :user_id],
             name: "source_snippets_unique_name_index"
           )

    create table(:source_lines, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :type, :text, null: false
      add :line_from, :bigint, null: false
      add :line_to, :bigint, null: false
      add :text, :text, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :file_id, :uuid, null: false
    end

    create table(:source_files, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:source_lines) do
      modify :file_id,
             references(:source_files,
               column: :id,
               name: "source_lines_file_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:source_files) do
      add :url, :text, null: false
      add :sha, :text, null: false
      add :owner, :text, null: false
      add :repository_id, :text, null: false
      add :filename, :text, null: false

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :snippet_id,
          references(:source_snippets,
            column: :id,
            name: "source_files_snippet_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end

    create table(:sections, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :body, :text, null: false
      add :book_id, :uuid, null: false
    end

    create table(:github_users, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :login, :text
      add :user_id, :uuid
    end

    create unique_index(:github_users, [:login, :user_id],
             name: "github_users_unique_login_index"
           )

    create table(:github_repositories, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :name, :text, null: false
      add :full_name, :text, null: false
      add :description, :text

      add :owner_id,
          references(:github_users,
            column: :id,
            name: "github_repositories_owner_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :visibility, :text, null: false, default: "public"
      add :user_id, :uuid, null: false

      add :last_sync_at, :utc_datetime,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :archived, :boolean, null: false, default: false
    end

    create unique_index(:github_repositories, [:name, :owner_id, :user_id],
             name: "github_repositories_unique_name_index"
           )

    create table(:github_commits, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :sha, :text

      add :repository_id,
          references(:github_repositories,
            column: :id,
            name: "github_commits_repository_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end

    create table(:books, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:sections) do
      modify :book_id,
             references(:books,
               column: :id,
               name: "sections_book_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:books) do
      add :title, :text, null: false

      add :user_id,
          references(:users,
            column: :id,
            name: "books_user_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end

    create table(:api_keys, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :api_key_hash, :binary, null: false
      add :expires_at, :utc_datetime_usec, null: false

      add :user_id,
          references(:users,
            column: :id,
            name: "api_keys_user_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end

    create unique_index(:api_keys, [:api_key_hash], name: "api_keys_unique_api_key_index")
  end

  def down do
    drop_if_exists unique_index(:api_keys, [:api_key_hash], name: "api_keys_unique_api_key_index")

    drop constraint(:api_keys, "api_keys_user_id_fkey")

    drop table(:api_keys)

    drop constraint(:books, "books_user_id_fkey")

    alter table(:books) do
      remove :user_id
      remove :title
    end

    drop constraint(:sections, "sections_book_id_fkey")

    alter table(:sections) do
      modify :book_id, :uuid
    end

    drop table(:books)

    drop constraint(:github_commits, "github_commits_repository_id_fkey")

    drop table(:github_commits)

    drop_if_exists unique_index(:github_repositories, [:name, :owner_id, :user_id],
                     name: "github_repositories_unique_name_index"
                   )

    drop constraint(:github_repositories, "github_repositories_owner_id_fkey")

    drop table(:github_repositories)

    drop_if_exists unique_index(:github_users, [:login, :user_id],
                     name: "github_users_unique_login_index"
                   )

    drop table(:github_users)

    drop table(:sections)

    drop constraint(:source_files, "source_files_snippet_id_fkey")

    alter table(:source_files) do
      remove :snippet_id
      remove :updated_at
      remove :inserted_at
      remove :filename
      remove :repository_id
      remove :owner
      remove :sha
      remove :url
    end

    drop constraint(:source_lines, "source_lines_file_id_fkey")

    alter table(:source_lines) do
      modify :file_id, :uuid
    end

    drop table(:source_files)

    drop table(:source_lines)

    drop_if_exists unique_index(:source_snippets, [:name, :user_id],
                     name: "source_snippets_unique_name_index"
                   )

    drop constraint(:source_snippets, "source_snippets_user_id_fkey")

    drop table(:source_snippets)

    drop table(:tokens)

    drop_if_exists unique_index(:users, [:email], name: "users_unique_email_index")

    drop table(:users)
  end
end
