{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "sha", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "github_commits_repository_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "github_repositories"}, "scale": null, "size": null, "source": "repository_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "A8B6EB23D638843CBF08FD3E43B213A32DDFABD5EE2271B177399204724E4A23", "identities": [], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.L3rnDev.Repo", "schema": null, "table": "github_commits"}