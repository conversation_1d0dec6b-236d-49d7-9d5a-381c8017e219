{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "login", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "user_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "632ECE26CFB3982637F1F38C821330D1CF48EF62444C0C673B15CA5B42C14EF4", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "github_users_unique_login_index", "keys": [{"type": "atom", "value": "login"}, {"type": "atom", "value": "user_id"}], "name": "unique_login", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.L3rnDev.Repo", "schema": null, "table": "github_users"}