{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "full_name", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "github_repositories_owner_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "github_users"}, "scale": null, "size": null, "source": "owner_id", "type": "uuid"}, {"allow_nil?": false, "default": "\"public\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "visibility", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "user_id", "type": "uuid"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "last_sync_at", "type": "utc_datetime"}, {"allow_nil?": false, "default": "false", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "archived", "type": "boolean"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "F97E5AF4467A4DE4890FE83319FB3FD6FB22CDF37828B0FE8855B0BA8DC5572B", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "github_repositories_unique_name_index", "keys": [{"type": "atom", "value": "name"}, {"type": "atom", "value": "owner_id"}, {"type": "atom", "value": "user_id"}], "name": "unique_name", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.L3rnDev.Repo", "schema": null, "table": "github_repositories"}