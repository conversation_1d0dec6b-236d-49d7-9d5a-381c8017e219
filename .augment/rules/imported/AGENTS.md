---
type: "manual"
---

# AI Agent Guidelines for l3rn_dev Project

## Core Principles

**Code Quality First**

- Follow existing conventions (style, naming, architecture) - analyze before coding
- Minimize code changes - atomic commits, less is more
- Self-documenting code only - avoid comments unless critical
- Security by default - never expose sensitive data or create vulnerabilities

**Scope Discipline**

- Execute only what's requested - ask before expanding scope
- Verify all dependencies exist in project manifests before use
- No assumptions about available libraries/frameworks

**AI-Specific Constraints**

- Always use available tools (get_source_location, get_package_location, read_file, search_file_content, etc.) - don't rely on assumed knowledge
- Provide reasoning for each decision in implementation
- When uncertain about project patterns, explicitly search for similar examples
- State assumptions clearly and verify them

## Workflow: Understand → Plan → Implement → Verify

### 1. Understand (MANDATORY Tool Usage)

**Required actions - execute these tools before any code changes:**

- `read_file` on target files and related modules
- `search_file_content` for similar patterns/implementations
- `glob` for understanding project structure
- Analyze user requirements against existing codebase

**AI Decision Points:**

- If patterns unclear → search for more examples
- If dependencies uncertain → check manifest files
- If conventions ambiguous → examine multiple similar files

### 2. Plan (Explicit Reasoning)

Create implementation plan with:

- Clear reasoning for each decision
- Specific files to modify and why
- Expected behavior changes
- Verification steps included

### 3. Implement (Tool-First Approach)

- Use absolute file paths always
- Execute independent operations in parallel
- Explain shell commands that modify system state
- Handle errors gracefully with clear messages
- **Before writing code**: verify current state with tools

### 4. Verify (Mandatory Sequence)

**Execute in order - do not skip steps:**

```bash
# 1. Write tests first (if applicable)
mix test --cover

# 2. Static analysis (required)
mix credo --strict
mix dialyzer

# 3. Review changes (required)
git status && git diff
```

## Tool Guidelines

**File Operations:** Absolute paths only
**Shell Commands:** Explain purpose and impact before execution
**GitHub Operations:** Use `gh` CLI over manual git operations
**Phoenix Server:** Use MCP tools for interaction; if running server needed, use `--port 4001`

## Project-Specific Rules

**Ash Framework**

- All data operations must use Ash policies and actors
- Ensure proper authorization context for all database access

**Database**

- Migrations must be reversible and production-safe
- Use configuration over hardcoded values

**LiveView**

- Single-file components with collocated templates (`~H` sigil)
- Client-side code via `<script :type={Phoenix.LiveView.ColocatedHook}>`
- Follow Phoenix LiveView state management conventions

**API Design**

- Authentication, validation, and error handling required
- Environment-appropriate configuration (dev/test/prod)

## Quality Checklist (AI Verification)

**Before Completion - Verify Each Item:**

- [ ] **Tool Usage**: Used get_ecto_schemas/list_ash_resources/get_usage_rules/list_generators/get_source_location/get_package_location/read_file/search_file_content to understand context
- [ ] **Pattern Matching**: Followed existing code patterns (provide examples)
- [ ] **Tests**: Written and passing (required for new functionality)
- [ ] **Test Coverage**: Analyzed `excoveralls` output from `mix test --cover` and ensured adequate coverage for new/changed code.
- [ ] **Static Analysis**: Clean credo and dialyzer output
- [ ] **Documentation**: Updated if changes affect public APIs
- [ ] **Scope**: Only implemented what was requested
- [ ] **Security**: No sensitive data exposed or vulnerabilities introduced
- [ ] **Performance**: Considered implications for database/large data operations
- [ ] **Backwards Compatibility**: No breaking changes or migration path provided
- [ ] **Resource Management**: Proper cleanup of connections/handles
- [ ] **Git Review**: Examined changes via `git diff`

**AI-Specific Verification:**

- [ ] **Reasoning**: Provided clear rationale for implementation decisions
- [ ] **Tool Usage**: Use project_eval, execute_sql_query, get_logs to verify implementation decisions
- [ ] **Assumptions**: Stated and verified all assumptions
- [ ] **Error Handling**: Addressed potential failure modes
- [ ] **Edge Cases**: Considered boundary conditions and error scenarios

## Anti-Patterns to Avoid

- Overengineering and unnecessary abstractions
- Introducing new patterns when existing ones work
- Shipping untested code
- Magic numbers/strings instead of constants
- Resource leaks (connections, handles)
- Inconsistent with existing codebase patterns
