defmodule L3rnDev.MixProject do
  use Mix.Project

  def project do
    [
      app: :l3rn_dev,
      version: "0.1.19",
      elixir: "~> 1.18",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps(),
      compilers: [:phoenix_live_view] ++ Mix.compilers(),
      elixirc_options: [ignore_module_conflict: true],
      listeners: [Phoenix.CodeReloader],
      test_coverage: [tool: ExCoveralls],
      coverage_options: [
        output_dir: "cover",
        # Other options are available, check excoveralls documentation
      ]
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {L3rnDev.Application, []},
      extra_applications: [:logger, :runtime_tools]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      {:ash_ai, "~> 0.2"},
      {:ash_authentication_phoenix, "~> 2.0"},
      {:ash_authentication, "~> 4.0"},
      {:bcrypt_elixir, "~> 3.0"},
      {:phoenix, "~> 1.8.0-rc.3", override: true},
      {:phoenix_ecto, "~> 4.5"},
      {:ecto_sql, "~> 3.10"},
      {:postgrex, ">= 0.0.0"},
      {:phoenix_html, "~> 4.1"},
      {:phoenix_live_reload, "~> 1.5.3", only: :dev},
      {:phoenix_live_view, "~> 1.1.0-rc.1"},
      {:lazy_html, ">= 0.0.0", only: :test},
      {:floki, ">= 0.30.0", only: :test},
      {:phoenix_live_dashboard, "~> 0.8.3"},
      {:esbuild, "~> 0.9", runtime: Mix.env() == :dev},
      {:tailwind, "~> 0.3.1", runtime: Mix.env() == :dev},
      {:heroicons,
       github: "tailwindlabs/heroicons",
       tag: "v2.2.0",
       sparse: "optimized",
       app: false,
       compile: false,
       depth: 1},
      {:swoosh, "~> 1.16"},
      {:telemetry_metrics, "~> 1.0"},
      {:telemetry_poller, "~> 1.0"},
      {:gettext, "~> 0.26"},
      {:jason, "~> 1.2"},
      {:dns_cluster, "~> 0.1.1"},
      {:bandit, "~> 1.5"},
      {:ueberauth, "~> 0.10"},
      {:ueberauth_github, "~> 0.8"},
      {:ex_aws, github: "ex-aws/ex_aws", override: true},
      {:ex_aws_s3, "~> 2.0"},
      {:hackney, "~> 1.9"},
      {:sweet_xml, "~> 0.6"},
      {:uuid, "~> 1.1"},
      {:dotenv, "~> 3.1.0", only: [:dev, :test]},
      {:req, "~> 0.5"},
      {:live_monaco_editor, "~> 0.2"},
      {:git_diff, "~> 0.6.4"},
      {:credo, "~> 1.7.12", only: [:dev, :test], runtime: false},
      {:timex, "~> 3.7"},
      {:ex_cldr_dates_times, "~> 2.21"},
      {:sentry, "~> 10.10"},
      {:ex_http_link, "~> 0.1.4"},
      {:ash, "~> 3.5"},
      {:ash_postgres, "~> 2.6"},
      {:ash_phoenix, "~> 2.3"},
      {:ash_oban, "~> 0.4.9"},
      {:picosat_elixir, "~> 0.2"},
      {:phoenix_storybook, "~> 0.8.0"},
      {:mcp_sse, "~> 0.1.3"},
      {:earmark, "~> 1.4.49", github: "pragdave/earmark", override: true},
      {:tidewave, "~> 0.1", only: :dev},
      {:live_debugger, "~> 0.3.0", only: :dev},
      {:oban, "~> 2.19"},
      {:oban_web, "~> 2.11"},
      {:igniter, "~> 0.5", only: [:dev]},
      {:dialyxir, "~> 1.4", only: [:dev, :test], runtime: false},
      {:sourceror, "~> 1.8", only: [:dev, :test]},
      {:open_api_spex, github: "bopm/open_api_spex", branch: "fix/otp-28", override: true},
      {:faker, "~> 0.10", only: :test},
      {:excoveralls, "~> 0.18.0", only: :test}
    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "esbuild.install --if-missing"],
      "assets.build": ["tailwind l3rn_dev", "esbuild l3rn_dev"],
      "assets.deploy": [
        "tailwind l3rn_dev --minify",
        "tailwind storybook --minify",
        "esbuild l3rn_dev --minify",
        "phx.digest"
      ],
      "sentry:package": ["sentry.package_source_code"]
    ]
  end
end
