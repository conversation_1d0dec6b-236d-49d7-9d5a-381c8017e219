defmodule L3rnDevWeb.CommitLive do
  alias L3rnDev.Source.Snippet
  use L3rnDevWeb, :live_view
  alias L3rnDevWeb.Components
  alias L3rnDevWeb.TailwindUi

  @impl true
  def render(assigns) do
    ~H"""
    <div class="mt-2 p-2">
      <TailwindUi.breadcrumbs>
        <:hop href={~p"/repositories"}>Repositories</:hop>
        <:hop href={~p"/repositories/#{@owner}/#{@name}"}>{@repository["full_name"]}</:hop>
        <:hop href={~p"/repositories/#{@owner}/#{@name}/#{@sha}"}>{@sha}</:hop>
      </TailwindUi.breadcrumbs>
      <div class="flex min-w-0 gap-x-4">
        <div class="min-w-0 flex-auto whitespace-pre-line text-xs font-mono">
          {Phoenix.HTML.raw(
            String.replace(String.trim_leading(@commit["commit"]["message"]), "\n", "<br/>")
          )}
        </div>
      </div>
    </div>
    <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end">
      <p class="text-sm leading-6 text-gray-900">{@sha}</p>
      <p class="mt-1 text-xs leading-5 text-gray-500">
        {@commit["author"]["login"]} committed
        <time datetime={@commit["commit"]["committer"]["date"]}>
          {Components.date_ago(@commit["commit"]["committer"]["date"])}
        </time>
      </p>
    </div>
    <div class="w-full relative z-30">
      <.patch :for={patch <- @parsed_diff} patch={patch} selected_lines={@selected_lines} />
    </div>

    <div
      :if={map_size(@selected_lines) > 0}
      class="fixed right-4 top-1/2 transform -translate-y-1/2 z-50"
    >
      <.button
        phx-click={TailwindUi.show_drawer("snippet-drawer")}
        class="rounded-full flex items-center justify-center px-4 py-4 shadow-lg hover:shadow-xl transition-all"
      >
        <svg
          class="w-5 h-5 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
          >
          </path>
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          >
          </path>
        </svg>
        Create Snippet
      </.button>
    </div>
    <.portal id="create-drawer" target="#drawer">
      <TailwindUi.drawer
        id="snippet-drawer"
        title="New Snippet"
        subtitle="Create code Snippet from selected diff lines"
        form={@snippet_form}
      >
        <div class="divide-y divide-gray-200 px-4 sm:px-6">
          <div class="space-y-6 pt-6 pb-5">
            <.input field={@snippet_form[:name]} label="Snippet Name" />
            <div>
              <label for="project-name" class="block text-sm/6 font-medium text-gray-900">
                Snippet Name
              </label>
              <div class="mt-2">
                <input
                  type="text"
                  name="project-name"
                  id="project-name"
                  class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus-visible:outline-2 focus-visible:-outline-offset-2 focus-visible:outline-indigo-600 sm:text-sm/6"
                />
              </div>
            </div>
            <div>
              <label for="project-description" class="block text-sm/6 font-medium text-gray-900">
                Description
              </label>
              <div class="mt-2">
                <textarea
                  rows="3"
                  name="project-description"
                  id="project-description"
                  class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus-visible:outline-2 focus-visible:-outline-offset-2 focus-visible:outline-indigo-600 sm:text-sm/6"
                ></textarea>
              </div>
            </div>
            <div>
              <h3 class="text-sm/6 font-medium text-gray-900">Team Members</h3>
              <div class="mt-2">
                <div class="flex space-x-2">
                  <a href="#" class="relative rounded-full hover:opacity-75">
                    <img
                      class="inline-block size-8 rounded-full"
                      src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                      alt="Tom Cook"
                    />
                  </a>
                  <a href="#" class="relative rounded-full hover:opacity-75">
                    <img
                      class="inline-block size-8 rounded-full"
                      src="https://images.unsplash.com/photo-1517365830460-955ce3ccd263?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                      alt="Whitney Francis"
                    />
                  </a>
                  <a href="#" class="relative rounded-full hover:opacity-75">
                    <img
                      class="inline-block size-8 rounded-full"
                      src="https://images.unsplash.com/photo-1519345182560-3f2917c472ef?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                      alt="Leonard Krasner"
                    />
                  </a>
                  <a href="#" class="relative rounded-full hover:opacity-75">
                    <img
                      class="inline-block size-8 rounded-full"
                      src="https://images.unsplash.com/photo-1463453091185-61582044d556?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                      alt="Floyd Miles"
                    />
                  </a>
                  <a href="#" class="relative rounded-full hover:opacity-75">
                    <img
                      class="inline-block size-8 rounded-full"
                      src="https://images.unsplash.com/photo-1502685104226-ee32379fefbe?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                      alt="Emily Selman"
                    />
                  </a>

                  <button
                    type="button"
                    class="relative inline-flex size-8 shrink-0 items-center justify-center rounded-full border-2 border-dashed border-gray-200 bg-white text-gray-400 hover:border-gray-300 hover:text-gray-500 focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2 focus-visible:outline-hidden"
                  >
                    <span class="absolute -inset-2"></span>
                    <span class="sr-only">Add team member</span>
                    <svg
                      class="size-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                      data-slot="icon"
                    >
                      <path d="M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5Z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <fieldset>
              <legend class="text-sm/6 font-medium text-gray-900">Privacy</legend>
              <div class="mt-2 space-y-4">
                <div class="relative flex items-start">
                  <div class="absolute flex h-6 items-center">
                    <input
                      id="privacy-public"
                      name="privacy"
                      value="public"
                      aria-describedby="privacy-public-description"
                      type="radio"
                      checked
                      class="relative size-4 appearance-none rounded-full border border-gray-300 before:absolute before:inset-1 before:rounded-full before:bg-white not-checked:before:hidden checked:border-indigo-600 checked:bg-indigo-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 forced-colors:appearance-auto forced-colors:before:hidden"
                    />
                  </div>
                  <div class="pl-7 text-sm/6">
                    <label for="privacy-public" class="font-medium text-gray-900">
                      Public access
                    </label>
                    <p id="privacy-public-description" class="text-gray-500">
                      Everyone with the link will see this project.
                    </p>
                  </div>
                </div>
                <div>
                  <div class="relative flex items-start">
                    <div class="absolute flex h-6 items-center">
                      <input
                        id="privacy-private-to-project"
                        name="privacy"
                        value="private-to-project"
                        aria-describedby="privacy-private-to-project-description"
                        type="radio"
                        class="relative size-4 appearance-none rounded-full border border-gray-300 before:absolute before:inset-1 before:rounded-full before:bg-white not-checked:before:hidden checked:border-indigo-600 checked:bg-indigo-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 forced-colors:appearance-auto forced-colors:before:hidden"
                      />
                    </div>
                    <div class="pl-7 text-sm/6">
                      <label for="privacy-private-to-project" class="font-medium text-gray-900">
                        Private to project members
                      </label>
                      <p id="privacy-private-to-project-description" class="text-gray-500">
                        Only members of this project would be able to access.
                      </p>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="relative flex items-start">
                    <div class="absolute flex h-6 items-center">
                      <input
                        id="privacy-private"
                        name="privacy"
                        value="private"
                        aria-describedby="privacy-private-to-project-description"
                        type="radio"
                        class="relative size-4 appearance-none rounded-full border border-gray-300 before:absolute before:inset-1 before:rounded-full before:bg-white not-checked:before:hidden checked:border-indigo-600 checked:bg-indigo-600 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:before:bg-gray-400 forced-colors:appearance-auto forced-colors:before:hidden"
                      />
                    </div>
                    <div class="pl-7 text-sm/6">
                      <label for="privacy-private" class="font-medium text-gray-900">
                        Private to you
                      </label>
                      <p id="privacy-private-description" class="text-gray-500">
                        You are the only one able to access this project.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </fieldset>
          </div>
          <div class="pt-4 pb-6">
            <div class="flex text-sm">
              <a
                href="#"
                class="group inline-flex items-center font-medium text-indigo-600 hover:text-indigo-900"
              >
                <svg
                  class="size-5 text-indigo-500 group-hover:text-indigo-900"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                  data-slot="icon"
                >
                  <path d="M12.232 4.232a2.5 2.5 0 0 1 3.536 3.536l-1.225 1.224a.75.75 0 0 0 1.061 1.06l1.224-1.224a4 4 0 0 0-5.656-5.656l-3 3a4 4 0 0 0 .225 5.865.75.75 0 0 0 .977-1.138 2.5 2.5 0 0 1-.142-3.667l3-3Z" />
                  <path d="M11.603 7.963a.75.75 0 0 0-.977 1.138 2.5 2.5 0 0 1 .142 3.667l-3 3a2.5 2.5 0 0 1-3.536-3.536l1.225-1.224a.75.75 0 0 0-1.061-1.06l-1.224 1.224a4 4 0 1 0 5.656 5.656l3-3a4 4 0 0 0-.225-5.865Z" />
                </svg>
                <span class="ml-2">Copy link</span>
              </a>
            </div>
            <div class="mt-4 flex text-sm">
              <a href="#" class="group inline-flex items-center text-gray-500 hover:text-gray-900">
                <svg
                  class="size-5 text-gray-400 group-hover:text-gray-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                  data-slot="icon"
                >
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0ZM8.94 6.94a.75.75 0 1 1-1.061-1.061 3 3 0 1 1 2.871 5.026v.345a.75.75 0 0 1-1.5 0v-.5c0-.72.57-1.172 1.081-1.287A1.5 1.5 0 1 0 8.94 6.94ZM10 15a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="ml-2">Learn more about sharing</span>
              </a>
            </div>
          </div>
        </div>
      </TailwindUi.drawer>
    </.portal>
    """
  end

  @impl true
  def mount(%{"name" => name, "owner" => owner, "sha" => sha}, _session, socket) do
    user = socket.assigns.current_user

    socket =
      case L3rnDev.Github.Api.get_repository(user, owner, name) do
        {:ok, repository, _} ->
          {:ok, commit, _} = L3rnDev.Github.Api.get_commit(user, owner, name, sha)
          {:ok, diff, _} = L3rnDev.Github.Api.get_commit_diff(user, owner, name, sha)
          {:ok, parsed} = GitDiff.parse_patch(diff)

          socket
          |> assign(
            current_email: user.email,
            owner: owner,
            name: name,
            sha: sha,
            repository: repository,
            commit: commit,
            diff: diff,
            parsed_diff: parsed,
            selected_lines: %{},
            snippet_form:
              Snippet
              |> AshPhoenix.Form.for_create(:create,
                as: "snippet",
                actor: socket.assigns.current_user
              )
              |> to_form()
          )

        {:error, :unauthorized} ->
          socket
          |> put_flash(:error, "Please sign in to view your commit.")
          |> redirect(to: ~p"/sign-out")

        _ ->
          socket
      end

    {:ok, socket, layout: {L3rnDevWeb.Layouts, :main}}
  end

  @impl true
  def handle_event("validate", %{"snippet" => params}, socket) do
    form = AshPhoenix.Form.validate(socket.assigns.snippet_form, params)
    {:noreply, assign(socket, :snippet_form, form)}
  end

  @impl true
  def handle_event("submit", %{"snippet" => params}, socket) do
    case AshPhoenix.Form.submit(socket.assigns.snippet_form,
           params: params
         ) do
      {:ok, _snippet} ->
        socket =
          socket
          |> put_flash(:success, "Snippet created successfully")

        {:noreply, socket}

      {:error, form} ->
        socket =
          socket
          |> put_flash(:error, "Something went wrong")
          |> assign(:snippet_form, form)

        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("select_line", params, socket) do
    selected_lines =
      socket.assigns.selected_lines
      |> Map.merge(%{
        params["file"] =>
          toggle_selected_line(
            socket.assigns.selected_lines,
            params["file"],
            params["from-line"],
            params["to-line"]
          )
          |> Enum.reject(&is_nil/1)
          |> Enum.uniq()
      })
      |> Map.filter(fn {_file, lines} -> lines != [] end)

    {:noreply, assign(socket, :selected_lines, selected_lines)}
  end

  defp toggle_selected_line(selected_lines, file, from, to) do
    selected_lines_value = get_selected_lines(selected_lines, file)

    if Enum.member?(selected_lines_value, %{from: from, to: to}) do
      selected_lines_value -- [%{from: from, to: to}]
    else
      selected_lines_value ++ [%{from: from, to: to}]
    end
  end

  defp get_selected_lines(selected_lines, file) do
    selected_lines[file] || []
  end
end
