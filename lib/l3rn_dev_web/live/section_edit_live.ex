defmodule L3rnDevWeb.SectionEditLive do
  use L3rnDevWeb, :live_view

  alias L3rnDev.Books.Book
  alias L3rnDev.Books.Section
  alias L3rnDevWeb.TailwindUi
  alias Phoenix.LiveView.ColocatedHook

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:content, "")
     |> assign(:selection, %{start: 0, end: 0})
     |> assign(:history, [])
     |> assign(:history_index, -1)
     |> allow_upload(:markdown_files,
       accept: ~w(.jpg .jpeg .png .gif .pdf .txt .md),
       max_entries: 10
     )}
  end

  @impl true
  def handle_params(params, _session, socket) do
    book = Ash.get!(Book, params["book_id"], actor: socket.assigns.current_user)

    section =
      Ash.get!(Section, params["id"],
        actor: socket.assigns.current_user,
        load: [
          book: [:sections]
        ]
      )

    form =
      AshPhoenix.Form.for_update(section, :update, actor: socket.assigns.current_user)
      |> to_form()

    {:noreply,
     socket
     |> assign(:book, book)
     |> assign(:section, section)
     |> assign(:form, form)
     |> assign(:content, section.body || "")}
  end

  @impl true
  def handle_event("content_changed", %{"content" => content, "selection" => selection}, socket) do
    socket =
      socket
      |> assign(:content, content)
      |> assign(:selection, selection)
      |> push_history(content)

    {:noreply, socket}
  end

  def handle_event("toolbar_action", %{"action" => action} = _params, socket) do
    handle_toolbar_action(action, socket)
  end

  def handle_event("insert_link", %{"text" => text, "url" => url}, socket) do
    {:noreply, push_event(socket, "insert_text", %{text: "[#{text}](#{url})"})}
  end

  def handle_event("validate", %{"form" => section_params}, socket) do
    {:noreply,
     assign(socket,
       form: AshPhoenix.Form.validate(socket.assigns.form, section_params)
     )}
  end

  def handle_event("save", %{"form" => section_params}, socket) do
    # Update the form with the current content
    params_with_content = Map.put(section_params, "body", socket.assigns.content)

    case AshPhoenix.Form.submit(socket.assigns.form, params: params_with_content) do
      {:ok, section} ->
        {:noreply,
         socket
         |> put_flash(:info, "Section updated successfully!")
         |> assign(:section, section)
         |> push_navigate(to: ~p"/books/#{socket.assigns.book.id}/sections/#{section.id}")}

      {:error, form} ->
        {:noreply, assign(socket, form: form)}
    end
  end

  def handle_event("upload_progress", %{"ref" => ref, "progress" => progress}, socket) do
    {:noreply, push_event(socket, "upload_progress", %{ref: ref, progress: progress})}
  end

  def handle_event("cancel_upload", %{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :markdown_files, ref)}
  end

  # Handle markdown toolbar actions - these are now handled client-side
  # but we keep the server handlers for any server-side logic needed
  defp handle_toolbar_action("bold", socket), do: {:noreply, socket}
  defp handle_toolbar_action("italic", socket), do: {:noreply, socket}
  defp handle_toolbar_action("quote", socket), do: {:noreply, socket}
  defp handle_toolbar_action("code", socket), do: {:noreply, socket}
  defp handle_toolbar_action("link", socket), do: {:noreply, socket}
  defp handle_toolbar_action("bulletList", socket), do: {:noreply, socket}
  defp handle_toolbar_action("numberList", socket), do: {:noreply, socket}
  defp handle_toolbar_action("undo", socket), do: handle_undo(socket)
  defp handle_toolbar_action("redo", socket), do: handle_redo(socket)
  defp handle_toolbar_action(_, socket), do: {:noreply, socket}

  def handle_progress(:markdown_files, entry, socket) do
    if entry.done? do
      # Process uploaded file
      file_path =
        consume_uploaded_entry(socket, entry, fn %{path: path} ->
          # Copy file to permanent location
          dest =
            Path.join([
              "priv",
              "static",
              "uploads",
              "#{entry.uuid}.#{get_extension(entry.client_name)}"
            ])

          File.cp!(path, dest)
          {:ok, "/uploads/#{entry.uuid}.#{get_extension(entry.client_name)}"}
        end)

      case file_path do
        {:ok, url} ->
          file_name = entry.client_name
          mime_type = entry.client_type

          socket =
            push_event(socket, "file_uploaded", %{
              name: file_name,
              url: url,
              mime_type: mime_type
            })

          {:noreply, socket}

        {:error, _} ->
          {:noreply, put_flash(socket, :error, "Failed to upload file")}
      end
    else
      {:noreply, socket}
    end
  end

  defp handle_undo(socket) do
    history = socket.assigns.history
    current_index = socket.assigns.history_index

    if current_index > 0 do
      new_index = current_index - 1
      content = Enum.at(history, new_index)

      socket =
        socket
        |> assign(:content, content)
        |> assign(:history_index, new_index)
        |> push_event("set_content", %{content: content})

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  defp handle_redo(socket) do
    history = socket.assigns.history
    current_index = socket.assigns.history_index

    if current_index < length(history) - 1 do
      new_index = current_index + 1
      content = Enum.at(history, new_index)

      socket =
        socket
        |> assign(:content, content)
        |> assign(:history_index, new_index)
        |> push_event("set_content", %{content: content})

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  defp push_history(socket, content) do
    history = socket.assigns.history
    current_index = socket.assigns.history_index

    # Don't add to history if content hasn't changed
    if current_index >= 0 and Enum.at(history, current_index) == content do
      socket
    else
      # Truncate history if we're not at the end
      new_history =
        if current_index == length(history) - 1 do
          history ++ [content]
        else
          Enum.take(history, current_index + 1) ++ [content]
        end

      # Keep only last 50 entries
      trimmed_history = Enum.take(new_history, -50)

      socket
      |> assign(:history, trimmed_history)
      |> assign(:history_index, length(trimmed_history) - 1)
    end
  end

  defp get_extension(filename) do
    filename
    |> Path.extname()
    |> String.trim_leading(".")
  end

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash}>
      <TailwindUi.breadcrumbs>
        <:hop href={~p"/books"}>Books</:hop>
        <:hop href={~p"/books/#{@book.id}"}>{@book.title}</:hop>
        <:hop href={~p"/books/#{@book.id}/sections/#{@section.id}"}>{@section.title}</:hop>
        <:hop>Edit</:hop>
      </TailwindUi.breadcrumbs>

    <!-- Full-height markdown editor within app layout -->
      <div
        id="markdown-editor-container"
        class="relative h-[calc(100vh-200px)]"
        phx-hook=".MarkdownEditor"
        data-uploads-url="/uploads"
        data-section-title={@section.title}
      >
        <!-- Hidden form for saving -->
        <.form for={@form} phx-submit="save" phx-change="validate" class="hidden">
          <input type="hidden" name="form[title]" value={@section.title} />
          <input type="hidden" name="form[body]" value={@content} />
        </.form>

    <!-- Scrollable content area (full height) -->
        <div class="h-full overflow-y-auto">
          <div
            class="markdown-editor-content whitespace-break-spaces w-full min-h-full text-left whitespace-pre-wrap caret-blue-500 focus:outline-none border-none resize-none p-6 pt-20 bg-white font-sans text-base leading-relaxed text-gray-700 relative"
            contenteditable="true"
            id="markdown-editor-content"
            data-placeholder="Start writing your markdown..."
            phx-update="ignore"
          ><%= @content %></div>
        </div>

    <!-- Floating toolbar (positioned over content) -->
        <div class="absolute top-6 left-1/2 transform -translate-x-1/2 z-10">
          <div
            class="inline-flex gap-0.5 bg-gray-50 rounded-full px-4 py-2 border border-gray-300 shadow-lg"
            id="markdown-editor"
          >
            <button
              data-markdown-action="bold"
              title="Bold"
              type="button"
              class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
            >
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
                <path
                  d="m4.1 23c-.5 0-.7-.4-.7-.7v-20.6c0-.4.4-.7.7-.7h8.9c2 0 3.8.6 4.9 1.5 1.2 1 1.8 2.4 1.8 4.1s-.9 3.2-2.3 4.1c-.2 0-.3.3-.3.5s0 .4.3.5c1.9.8 3.2 2.7 3.2 5s-.7 3.6-2.1 4.7-3.3 1.7-5.6 1.7h-8.8zm4.2-18.1v5.1h3c1.2 0 2-.3 2.7-.7.6-.5.9-1.1.9-1.9s-.3-1.4-.8-1.8-1.3-.6-2.3-.6-2.4 0-3.5 0zm0 8.5v5.8h3.7c1.3 0 2.2-.3 2.8-.7s.9-1.2.9-2.2-.4-1.7-1-2.1-1.7-.7-2.9-.7-2.4 0-3.5 0z"
                  fill-rule="evenodd"
                />
              </svg>
            </button>

            <button
              data-markdown-action="italic"
              title="Italic"
              type="button"
              class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
            >
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
                <path
                  d="m9.3 1h10.2v3.1h-3.5l-3.7 15.7h3.2v3.2h-11v-3.1h3.8l3.7-15.7h-2.8v-3.2z"
                  fill-rule="evenodd"
                />
              </svg>
            </button>

            <button
              data-markdown-action="quote"
              title="Quote"
              type="button"
              class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
            >
              <svg viewBox="0 0 24 22" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
                <path d="m1.1 5.2c.6-.7 1.4-1.3 2.4-1.4 2.6-.4 4.2.4 5.3 1.9 2 2.3 1.9 5.1.6 7.6-1.3 2.4-4 4.6-7.2 5.1-.4 0-.7-.1-1-.4-.1-.3-.1-.7.3-1.1l1.1-1.1c.3-.4.6-.7.7-1.1s.3-.9 0-1.3c0-.4-.6-.7-1-1-1.2-.8-2.3-2.2-2.3-4.1.1-1.4.4-2.4 1.1-3.1z" />
                <path d="m14.6 5.2c.6-.7 1.6-1.1 2.6-1.4 2.4-.4 4.2.4 5.3 1.9 2 2.3 1.9 5.1.6 7.6-1.3 2.4-4 4.6-7.2 5.1-.4 0-.7-.1-1-.4-.1-.3-.1-.7.3-1.1l1.1-1.1c.3-.4.6-.7.7-1.1s.3-.9 0-1.3c-.1-.4-.6-.7-1-1-1.3-.6-2.4-2-2.4-3.9s.4-2.6 1-3.3z" />
              </svg>
            </button>

            <button
              data-markdown-action="code"
              title="Code"
              type="button"
              class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
            >
              <svg viewBox="0 0 24 22" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
                <path
                  d="m.4 10.1c-.5.5-.5 1.4 0 1.9l5.3 5.3c.5.5 1.4.5 1.9 0s.5-1.4 0-1.9l-4.4-4.4 4.4-4.4c.5-.5.5-1.4 0-1.9s-1.3-.5-1.9 0c0 0-5.3 5.4-5.3 5.4zm17.9 7.2 5.3-5.3c.5-.5.5-1.4 0-1.9l-5.3-5.3c-.5-.5-1.4-.5-1.9 0s-.5 1.4 0 1.9l4.4 4.4-4.4 4.4c-.5.5-.5 1.4 0 1.9.5.4 1.4.4 1.9-.1z"
                  fill-rule="evenodd"
                />
              </svg>
            </button>

            <button
              data-markdown-action="link"
              title="Link"
              type="button"
              class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
            >
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
                <path d="m9.4 14.5c-2.3-2.3-2.3-5.9 0-8.2l4.6-4.6c1.1-1 2.6-1.7 4.2-1.7s3 .5 4.1 1.7c2.3 2.3 2.3 5.9 0 8.2l-2.7 2.3c-.5.5-1.2.5-1.8 0-.5-.5-.5-1.2 0-1.7l2.7-2.3c1.4-1.3 1.4-3.4 0-4.7-.7-.7-1.5-.9-2.3-.9s-1.8.4-2.5.9l-4.7 4.5c-1.4 1.3-1.4 3.4 0 4.7.5.5.5 1.2 0 1.7-.1.3-.4.4-.8.4s-.5-.1-.8-.3z" />
                <path d="m1.7 22.3c-2.3-2.3-2.3-5.9 0-8.2l2.6-2.5c.5-.5 1.2-.5 1.8 0 .5.5.5 1.2 0 1.7l-2.6 2.5c-1.4 1.3-1.4 3.4 0 4.7.7.7 1.5.9 2.3.9s1.8-.4 2.3-.9l4.6-4.6c1.4-1.3 1.4-3.4 0-4.7-.5-.4-.5-1.2 0-1.7s1.2-.5 1.8 0c2.3 2.3 2.3 5.9 0 8.2l-4.6 4.6c-1 1-2.5 1.7-4.1 1.7s-3-.7-4.1-1.7z" />
              </svg>
            </button>

            <label
              title="Add Image"
              class="flex items-center justify-center w-8 h-8 border-none bg-transparent rounded-md cursor-pointer text-gray-600 transition-all duration-200 ease-in-out hover:bg-gray-200 hover:text-gray-900 active:bg-gray-300 hover:scale-105"
            >
              <svg viewBox="0 0 24 20" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-current">
                <path
                  d="m22 20h-20c-1.1 0-2-.9-2-2.1v-15.8c0-1.2.9-2.1 2-2.1h20c1.1 0 2 .9 2 2.1v15.8c0 1.1-.9 2.1-2 2.1zm0-2.9v-14.5c0-.3-.2-.5-.5-.5h-19c-.3 0-.5.2-.5.5v14.5c0 .1.1.2.2.2s.2 0 .2-.1l2.2-3.3c.1-.2.3-.3.5-.3h.7l2.6-4c.1-.2.3-.3.5-.3h.7c.2 0 .4.1.5.3l5.3 8c0 .1.2.2.3.2h.3c.2 0 .4-.2.4-.4s0-.2 0-.2l-1.3-1.9c-.2-.2-.2-.6 0-.8l1.2-1.6c.1-.2.3-.3.5-.3h1.1c.2 0 .4 0 .5.3l3.2 4.4c0 .1.3.2.4 0 .2 0 .2 0 .2-.2zm-5.5-7.6c-1.4 0-2.5-1.2-2.5-2.6s1.1-2.6 2.5-2.6 2.5 1.2 2.5 2.6-1.1 2.6-2.5 2.6z"
                  fill-rule="evenodd"
                />
              </svg>
              <.live_file_input
                upload={@uploads.markdown_files}
                data-markdown-toolbar-file-picker="true"
                class="hidden"
              />
            </label>
          </div>
        </div>

    <!-- Save button (floating) -->
        <!-- Save button (floating within the editor area) -->
        <div class="absolute bottom-6 right-6">
          <button
            id="save-button"
            class="px-6 py-3 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors duration-200 shadow-lg"
          >
            ✓
          </button>
        </div>

    <!-- Upload Progress -->
        <%= for entry <- @uploads.markdown_files.entries do %>
          <div
            class="absolute bottom-20 right-6 rounded-lg bg-white shadow-lg p-4 border"
            data-ref={entry.ref}
          >
            <%= if entry.cancelled? do %>
              <button
                class="absolute top-2 right-2 w-4 h-4 bg-transparent border-none cursor-pointer outline-none text-gray-500 font-bold text-xl flex items-center justify-center"
                phx-click="cancel_upload"
                phx-value-ref={entry.ref}
                aria-label="Close"
              >
                ×
              </button>
              <div class="text-red-500 font-medium">
                Upload failed: {entry.client_name}
              </div>
            <% else %>
              <div class="text-gray-700 font-medium mb-2">
                {entry.client_name}
              </div>
              <progress
                class="w-full h-2 bg-gray-200 rounded-lg"
                max="100"
                value={entry.progress}
                style={if entry.done?, do: "display: none", else: "display: block"}
              >
                {entry.progress}%
              </progress>
            <% end %>
          </div>
        <% end %>
      </div>
    </Layouts.app>

    <script :type={ColocatedHook} name=".MarkdownEditor">
      export default {
          mounted() {
            // Find content element and save button
            this.contentElement = document.querySelector('#markdown-editor-content');
            this.saveButton = document.querySelector('#save-button');

            // Setup content element
            this.contentElement.focus();
            this.setupPlaceholder();
            this.setupInputHandling();
            this.setupMarkdownRendering();

            // Ensure content element is found
            if (!this.contentElement) {
              console.error('Content element not found');
              return;
            }

            this.setupEventListeners();

            // Initial render with a small delay to ensure DOM is ready
            setTimeout(() => {
              this.renderContent();
            }, 100);
          },

          setupEventListeners() {
            // Handle toolbar actions
            this.el.addEventListener('click', (e) => {
              const button = e.target.closest('[data-markdown-action]');
              if (button) {
                e.preventDefault();
                const action = button.dataset.markdownAction;
                this.handleToolbarAction(action);
              }
            });

            // Handle save button
            this.saveButton.addEventListener('click', (e) => {
              e.preventDefault();
              this.saveContent();
            });

            // Handle file picker
            const filePicker = this.el.querySelector('[data-markdown-toolbar-file-picker]');
            if (filePicker) {
              filePicker.addEventListener('change', (e) => {
                for (const file of e.target.files) {
                  this.handleFileUpload(file);
                }
              });
            }

            // Handle keyboard shortcuts and special keys
            this.contentElement.addEventListener('keydown', (e) => {
              if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                  case 'b':
                    e.preventDefault();
                    this.handleToolbarAction('bold');
                    break;
                  case 'i':
                    e.preventDefault();
                    this.handleToolbarAction('italic');
                    break;
                  case 'z':
                    e.preventDefault();
                    if (e.shiftKey) {
                      this.handleToolbarAction('redo');
                    } else {
                      this.handleToolbarAction('undo');
                    }
                    break;
                }
              } else if (e.key === 'Enter') {
                // Handle Enter key specially to ensure proper line breaks
                e.preventDefault();
                this.insertLineBreak();
              }
            });

            // Handle content changes for live highlighting - removed duplicate handlers
            // (these are now handled in setupInputHandling method)

            // Handle drag and drop
            this.contentElement.addEventListener('dragover', (e) => {
              e.preventDefault();
              this.contentElement.classList.add('bg-blue-50');
            });

            this.contentElement.addEventListener('dragleave', (e) => {
              this.contentElement.classList.remove('bg-blue-50');
            });

            this.contentElement.addEventListener('drop', (e) => {
              e.preventDefault();
              this.contentElement.classList.remove('bg-blue-50');

              const files = Array.from(e.dataTransfer.files);
              files.forEach(file => this.handleFileUpload(file));
            });
          },

          handleToolbarAction(action) {
            this.contentElement.focus();

            switch(action) {
              case 'bold':
                this.toggleFormat('**', '**');
                break;
              case 'italic':
                this.toggleFormat('*', '*');
                break;
              case 'quote':
                this.toggleLineFormat('> ');
                break;
              case 'code':
                this.toggleCodeFormat();
                break;
              case 'link':
                this.insertLink();
                break;
              case 'bulletList':
                this.toggleLineFormat('- ');
                break;
              case 'numberList':
                this.toggleLineFormat('1. ');
                break;
            }
          },

          toggleFormat(startMark, endMark) {
            const selection = window.getSelection();

            // If no selection, create one or insert at cursor
            if (selection.rangeCount === 0) {
              // Create a range at the end of content
              const range = document.createRange();
              range.selectNodeContents(this.contentElement);
              range.collapse(false);
              selection.removeAllRanges();
              selection.addRange(range);
            }

            const range = selection.getRangeAt(0);
            const selectedText = range.toString();

            let newText;
            if (selectedText.length === 0) {
              // No text selected, insert formatting markers with placeholder
              newText = `${startMark}text${endMark}`;
            } else if (selectedText.startsWith(startMark) && selectedText.endsWith(endMark)) {
              // Remove formatting
              newText = selectedText.slice(startMark.length, -endMark.length);
            } else {
              // Add formatting
              newText = `${startMark}${selectedText}${endMark}`;
            }

            this.replaceSelection(newText);
          },

          toggleLineFormat(prefix) {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return;

            const range = selection.getRangeAt(0);
            const selectedText = range.toString();

            const lines = selectedText.split('\n');
            const newLines = lines.map(line => {
              if (line.startsWith(prefix)) {
                return line.slice(prefix.length);
              } else {
                return prefix + line;
              }
            });

            this.replaceSelection(newLines.join('\n'));
          },

          toggleCodeFormat() {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return;

            const selectedText = selection.getRangeAt(0).toString();

            if (selectedText.includes('\n')) {
              // Multi-line code block
              this.toggleFormat('```\n', '\n```');
            } else {
              // Inline code
              this.toggleFormat('`', '`');
            }
          },

          insertLink() {
            const selection = window.getSelection();
            const selectedText = selection.rangeCount > 0 ? selection.getRangeAt(0).toString() : '';

            const linkText = selectedText || 'link text';
            const linkUrl = prompt('Enter URL:', 'https://');

            if (linkUrl) {
              this.replaceSelection(`[${linkText}](${linkUrl})`);
            }
          },

          handleFileUpload(file) {
            // Create upload element
            const uploadElement = document.createElement('markdown-editor-upload');
            uploadElement.setAttribute('status', 'uploading');
            uploadElement.innerHTML = `
              <div class="md-file font-normal">${file.name}</div>
              <progress class="md-progress-bar w-full h-2 bg-gray-200 rounded-lg" max="100" value="0">0%</progress>
            `;
            this.el.appendChild(uploadElement);

            // Simulate upload progress (in real app, this would be handled by LiveView uploads)
            let progress = 0;
            const interval = setInterval(() => {
              progress += 10;
              const progressBar = uploadElement.querySelector('progress');
              if (progressBar) {
                progressBar.value = progress;
                progressBar.textContent = `${progress}%`;
              }

              if (progress >= 100) {
                clearInterval(interval);
                uploadElement.setAttribute('status', 'complete');
                uploadElement.querySelector('progress').style.display = 'none';

                // Insert file reference into content
                const isImage = file.type.startsWith('image/');
                const fileRef = isImage ? `![${file.name}](uploads/${file.name})` : `[${file.name}](uploads/${file.name})`;
                this.insertTextAtCursor(fileRef);

                // Remove upload element after delay
                setTimeout(() => uploadElement.remove(), 2000);
              }
            }, 200);
          },

          insertTextAtCursor(text) {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return;

            const range = selection.getRangeAt(0);
            range.insertNode(document.createTextNode(text));
            range.setStartAfter(range.endContainer);
            range.collapse(true);

            this.updateContent();
          },

          insertLineBreak() {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return;

            const range = selection.getRangeAt(0);

            // Insert a newline character
            const textNode = document.createTextNode('\n');
            range.insertNode(textNode);

            // Position cursor after the newline
            range.setStartAfter(textNode);
            range.collapse(true);
            selection.removeAllRanges();
            selection.addRange(range);

            // Update content without immediate re-rendering to avoid cursor jump
            this.deferredUpdateContent();
          },

          replaceSelection(newText) {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return;

            const range = selection.getRangeAt(0);
            range.deleteContents();
            range.insertNode(document.createTextNode(newText));

            // Move cursor to end of inserted text
            range.setStartAfter(range.endContainer);
            range.collapse(true);
            selection.removeAllRanges();
            selection.addRange(range);

            this.updateContent();
          },

          updateContent() {
            const content = this.contentElement.textContent;
            this.pushEvent('content_changed', {
              content: content,
              selection: { start: 0, end: 0 }
            });

            this.renderContent();
          },

          deferredUpdateContent() {
            // Save cursor position before any DOM changes
            const selection = window.getSelection();
            const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
            const cursorInfo = this.saveCursorPosition();

            const content = this.contentElement.textContent;
            this.pushEvent('content_changed', {
              content: content,
              selection: { start: 0, end: 0 }
            });

            // Use requestAnimationFrame to defer rendering and cursor restoration
            requestAnimationFrame(() => {
              this.renderContentWithCursor(cursorInfo);
            });
          },

          renderContent() {
            const cursorInfo = this.saveCursorPosition();
            this.renderContentWithCursor(cursorInfo);
          },

          saveCursorPosition() {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return null;

            const range = selection.getRangeAt(0);
            const preCaretRange = range.cloneRange();
            preCaretRange.selectNodeContents(this.contentElement);
            preCaretRange.setEnd(range.startContainer, range.startOffset);

            return {
              offset: preCaretRange.toString().length,
              content: this.contentElement.textContent
            };
          },

          renderContentWithCursor(cursorInfo) {
            // Apply markdown syntax highlighting
            const content = this.contentElement.textContent || '';
            const highlighted = this.highlightMarkdown(content);

            this.contentElement.innerHTML = highlighted;

            // Restore cursor position using improved logic
            if (cursorInfo && cursorInfo.offset !== undefined) {
              this.restoreCursorPosition(cursorInfo.offset);
            }

            // Update placeholder state
            if (content.trim() === '') {
              this.contentElement.classList.add('empty');
            } else {
              this.contentElement.classList.remove('empty');
            }
          },

          restoreCursorPosition(offset) {
            const selection = window.getSelection();
            try {
              const textNode = this.getTextNodeAtOffset(this.contentElement, offset);
              if (textNode) {
                const range = document.createRange();
                range.setStart(textNode.node, Math.min(textNode.offset, textNode.node.textContent.length));
                range.collapse(true);
                selection.removeAllRanges();
                selection.addRange(range);
              } else {
                // Fallback: place cursor at end
                this.placeCursorAtEnd();
              }
            } catch (e) {
              // Fallback: place cursor at end
              this.placeCursorAtEnd();
            }
          },

          placeCursorAtEnd() {
            const selection = window.getSelection();
            const range = document.createRange();
            range.selectNodeContents(this.contentElement);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
          },

          getTextNodeAtOffset(root, offset) {
            let currentOffset = 0;
            const walker = document.createTreeWalker(
              root,
              NodeFilter.SHOW_TEXT,
              null,
              false
            );

            let node;
            while (node = walker.nextNode()) {
              const nodeLength = node.textContent.length;
              if (currentOffset + nodeLength >= offset) {
                return {
                  node: node,
                  offset: offset - currentOffset
                };
              }
              currentOffset += nodeLength;
            }

            return null;
          },

          highlightMarkdown(text) {
            // Markdown syntax highlighting that preserves the original markdown markers
            return text
              .replace(/(\*\*)([^*]+)(\*\*)/g, '<span class="font-bold text-gray-800"><span class="text-gray-400">$1</span>$2<span class="text-gray-400">$3</span></span>')
              .replace(/(\*)([^*]+)(\*)/g, '<span class="italic text-gray-700"><span class="text-gray-400">$1</span>$2<span class="text-gray-400">$3</span></span>')
              .replace(/(~~)([^~]+)(~~)/g, '<span class="line-through text-gray-500"><span class="text-gray-400">$1</span>$2<span class="text-gray-400">$3</span></span>')
              .replace(/(`+)([^`]+)(\1)/g, '<span class="bg-gray-100 px-1 py-0.5 rounded font-mono text-sm"><span class="text-gray-400">$1</span>$2<span class="text-gray-400">$3</span></span>')
              .replace(/(^|\n)(#{1,6})\s+([^\n]+)/g, '$1<span class="font-bold text-gray-700"><span class="text-gray-400">$2</span> $3</span>')
              .replace(/\n/g, '<br>');
          },

          updated() {
            this.setupPlaceholder();
          },

          setupPlaceholder() {
            const placeholder = this.contentElement.dataset.placeholder;
            if (this.contentElement.textContent.trim() === '') {
              this.contentElement.classList.add('empty');
              if (!this.contentElement.querySelector('.placeholder')) {
                const placeholderEl = document.createElement('span');
                placeholderEl.className = 'placeholder absolute text-gray-400 pointer-events-none';
                placeholderEl.textContent = placeholder;
                this.contentElement.appendChild(placeholderEl);
              }
            } else {
              this.contentElement.classList.remove('empty');
              const placeholderEl = this.contentElement.querySelector('.placeholder');
              if (placeholderEl) {
                placeholderEl.remove();
              }
            }
          },

          setupInputHandling() {
            // Use deferred rendering for better cursor handling
            this.contentElement.addEventListener('input', (e) => {
              // For regular input, use deferred update to preserve cursor position
              this.deferredUpdateContent();
              this.setupPlaceholder();
            });

            this.contentElement.addEventListener('keyup', (e) => {
              // Only render on keyup for non-navigation keys to avoid cursor jumps
              if (!['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(e.key)) {
                this.deferredUpdateContent();
              }
            });

            this.contentElement.addEventListener('paste', (e) => {
              // Use longer delay for paste to ensure content is fully processed
              setTimeout(() => this.deferredUpdateContent(), 50);
            });

            this.contentElement.addEventListener('focus', () => {
              const placeholderEl = this.contentElement.querySelector('.placeholder');
              if (placeholderEl) {
                placeholderEl.style.display = 'none';
              }
            });

            this.contentElement.addEventListener('blur', () => {
              this.setupPlaceholder();
            });
          },

          setupMarkdownRendering() {
            // Initial render
            setTimeout(() => {
              this.renderContent();
            }, 100);
          },

          saveContent() {
            const content = this.contentElement.textContent || '';
            this.pushEvent('save', {
              form: {
                title: this.el.dataset.sectionTitle || '',
                body: content
              }
            });
          }
        };
    </script>
    """
  end
end
