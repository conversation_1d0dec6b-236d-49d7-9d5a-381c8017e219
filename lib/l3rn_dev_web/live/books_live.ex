defmodule L3rnDevWeb.BooksLive do
  alias L3rnDev.Books.Book
  use L3rnDevWeb, :live_view

  @impl true
  def render(assigns) do
    ~H"""
    <%= if @live_action == :index do %>
      <Layouts.app flash={@flash}>
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Books
          </h1>
          <div :if={Enum.count(@books) == 0} class="text-center">
            <.icon name="hero-book-open" class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-semibold text-gray-900">No books yet</h3>
            <p class="mt-1 text-sm text-gray-500">Get started by creating a new book.</p>
            <div class="mt-6">
              <.link
                patch={~p"/books/new"}
                class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                <.icon name="hero-plus" class="-ml-0.5 mr-1.5 h-5 w-5" /> New Book
              </.link>
            </div>
          </div>

          <ul
            :if={Enum.count(@books) > 0}
            role="list"
            class="mt-3 grid grid-cols-1 gap-5 sm:grid-cols-2 sm:gap-6 lg:grid-cols-4"
          >
            <li :for={book <- @books} class="col-span-1 flex rounded-md shadow-xs">
              <div class="aspect-[6/9] rounded-[0.3em] border border-subtle-dark text-inherit block
                   text-[clamp(0.5rem,5cqi,0.8rem)] leading-[1.4] mb-[var(--block-space-half)] overflow-hidden p-[5cqi]
                   relative text-start no-underline">
                <.link
                  patch={~p"/books/#{book.id}"}
                  class="bg-transparent content-[''] inset-0 absolute z-[1]"
                >
                  <span class="sr-only">Open {book.title}</span>
                </.link>
                {book.title}
              </div>
            </li>
            <li class="col-span-1 flex rounded-md shadow-xs">
              <div class="aspect-[6/9] rounded-[0.3em] border border-subtle-dark text-inherit block
                    leading-[1.4] mb-[var(--block-space-half)] overflow-hidden p-[5cqi]
                   relative text-start no-underline">
                <.link patch={~p"/books/new"}>
                  <.icon name="hero-plus" class="-ml-0.5 mr-1.5 h-5 w-5" /> New Book
                </.link>
              </div>
            </li>
          </ul>
        </div>
      </Layouts.app>
    <% end %>
    <%= if @live_action == :new do %>
      <Layouts.app flash={@flash}>
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            New Book
          </h1>
          <.form
            :let={f}
            for={@form}
            id="book-form"
            phx-change="validate"
            phx-submit="create"
            class="mt-8 space-y-6"
          >
            <.input field={f[:title]} type="text" label="Title" />
            <div class="mt-6">
              <button
                type="submit"
                class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Create Book
              </button>
            </div>
          </.form>
        </div>
      </Layouts.app>
    <% end %>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(_params, _session, socket) do
    case socket.assigns.live_action do
      :index ->
        {:noreply, assign(socket, books: Ash.read!(Book, actor: socket.assigns.current_user))}

      :new ->
        {:noreply,
         assign(socket,
           form:
             AshPhoenix.Form.for_create(Book, :create, actor: socket.assigns.current_user)
             |> to_form()
         )}
    end
  end

  @impl true
  def handle_event("validate", %{"form" => book_params}, socket) do
    {:noreply,
     assign(socket,
       form: AshPhoenix.Form.validate(socket.assigns.form, book_params)
     )}
  end

  @impl true
  def handle_event("create", %{"form" => book_params}, socket) do
    case AshPhoenix.Form.submit(socket.assigns.form, params: book_params) do
      {:ok, book} ->
        {:noreply,
         socket
         |> put_flash(:info, "Book created successfully!")
         |> push_navigate(to: ~p"/books/#{book.id}")}

      {:error, form} ->
        {:noreply, assign(socket, form: form)}
    end
  end
end
