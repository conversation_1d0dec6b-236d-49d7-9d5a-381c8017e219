defmodule L3rnDevWeb.BookLive do
  alias L3rnDev.Books.Book
  use L3rnDevWeb, :live_view
  alias L3rnDevWeb.TailwindUi

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash}>
      <TailwindUi.breadcrumbs>
        <:hop href={~p"/books"}>Books</:hop>
        <:hop href={~p"/books/#{@book.id}"}>{@book.title}</:hop>
      </TailwindUi.breadcrumbs>

      <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
        {@book.title}
      </h1>

      <div :if={Enum.count(@book.sections) == 0} class="text-center">
        <.icon name="hero-book-open" class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-semibold text-gray-900">No Sections yet</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new section.</p>
        <div class="mt-6">
          <.link
            patch={~p"/books/#{@book.id}/sections/new"}
            class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            <.icon name="hero-plus" class="-ml-0.5 mr-1.5 h-5 w-5" /> New Section
          </.link>
        </div>
      </div>

      <ul
        :if={Enum.count(@book.sections) > 0}
        role="list"
        class="mt-3 grid grid-cols-1 gap-5 sm:grid-cols-2 sm:gap-6 lg:grid-cols-4"
      >
        <li :for={section <- @book.sections} class="col-span-1 flex rounded-md shadow-xs">
          <div class="aspect-[6/9] rounded-[0.3em] border border-subtle-dark text-inherit block
               text-[clamp(0.5rem,5cqi,0.8rem)] leading-[1.4] mb-[var(--block-space-half)] overflow-hidden p-[5cqi]
               relative text-start no-underline">
            <.link
              patch={~p"/books/#{@book.id}/sections/#{section.id}"}
              class="bg-transparent content-[''] inset-0 absolute z-[1]"
            >
              <span class="sr-only">Open {section.title}</span>
            </.link>
            {section.body}
          </div>
        </li>
        <li class="col-span-1 flex rounded-md shadow-xs">
          <div class="aspect-[6/9] rounded-[0.3em] border border-subtle-dark text-inherit block
                leading-[1.4] mb-[var(--block-space-half)] overflow-hidden p-[5cqi]
               relative text-start no-underline">
            <.link patch={~p"/books/#{@book.id}/sections/new"}>
              <.icon name="hero-plus" class="-ml-0.5 mr-1.5 h-5 w-5" /> New Section
            </.link>
          </div>
        </li>
      </ul>
    </Layouts.app>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(params, _session, socket) do
    {:noreply,
     assign(socket,
       book: Ash.get!(Book, params["id"], load: [:sections], actor: socket.assigns.current_user)
     )}
  end
end
