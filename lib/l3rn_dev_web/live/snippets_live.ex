defmodule L3rnDevWeb.SnippetsLive do
  alias L3rnDev.Source.Snippet
  use L3rnDevWeb, :live_view

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash}>
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div :if={Enum.count(@snippets) == 0} class="text-center">
          <.icon name="hero-folder-plus" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-semibold text-gray-900">No snippets</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by creating a new snippet.</p>
          <div class="mt-6">
            <.link
              patch={~p"/snippets/new"}
              class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              <.icon name="hero-plus" class="-ml-0.5 mr-1.5 h-5 w-5" /> New Snippet
            </.link>
          </div>
        </div>
        <ul :if={Enum.count(@snippets) > 0} role="list" class="divide-y divide-gray-100" id="snippets">
          <li :for={snippet <- @snippets} class="flex justify-between gap-x-6 py-5">
            <div class="min-w-0">
              <div class="flex items-start gap-x-3">
                <p class="text-sm font-semibold leading-6 text-gray-900">{snippet.name}</p>
              </div>
            </div>
            <div class="flex flex-none items-center gap-x-4">
              <a
                href={~p"/snippets/#{snippet.id}"}
                class="hidden rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:block"
              >
                View snippet<span class="sr-only">, <%= snippet.name %></span>
              </a>
            </div>
          </li>
        </ul>
      </div>
    </Layouts.app>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, snippets: Ash.read!(Snippet, actor: socket.assigns.current_user))}
  end
end
