defmodule L3rnDevWeb.SectionLive do
  use L3rnDevWeb, :live_view

  alias L3rnDev.Books.Book
  alias L3rnDev.Books.Section
  alias L3rnDevWeb.TailwindUi

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash}>
      <TailwindUi.breadcrumbs>
        <:hop href={~p"/books"}>Books</:hop>
        <:hop href={~p"/books/#{@book.id}"}>{@book.title}</:hop>
        <:hop href={~p"/books/#{@book.id}/sections/#{@section.id}"}>{@section.title}</:hop>
      </TailwindUi.breadcrumbs>

      <div class="flex justify-between items-start mb-6">
        <div>
          <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            {@book.title}
          </h1>
          <h2 class="text-xl font-bold leading-7 text-gray-900 sm:truncate sm:text-2xl sm:tracking-tight">
            {@section.title}
          </h2>
        </div>
        <.link
          href={~p"/books/#{@book.id}/sections/#{@section.id}/edit"}
          class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          <.icon name="hero-pencil" class="-ml-0.5 mr-1.5 h-4 w-4" />
          Edit Section
        </.link>
      </div>

      <div class="prose max-w-none">
        <%= if @section.body && String.trim(@section.body) != "" do %>
          <%= raw(render_markdown(@section.body)) %>
        <% else %>
          <p class="text-gray-500 italic">This section is empty. Click "Edit Section" to add content.</p>
        <% end %>
      </div>
    </Layouts.app>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(params, _session, socket) do
    book = Ash.get!(Book, params["book_id"], actor: socket.assigns.current_user)

    section =
      Ash.get!(Section, params["id"],
        actor: socket.assigns.current_user,
        load: [
          book: [:sections]
        ]
      )

    {:noreply,
     socket
     |> assign(:book, book)
     |> assign(:section, section)}
  end

  # Simple markdown rendering for display
  defp render_markdown(content) do
    content
    |> String.replace(~r/\*\*(.*?)\*\*/, "<strong>\\1</strong>")
    |> String.replace(~r/\*(.*?)\*/, "<em>\\1</em>")
    |> String.replace(~r/~~(.*?)~~/, "<del>\\1</del>")
    |> String.replace(~r/`(.*?)`/, "<code>\\1</code>")
    |> String.replace(~r/^# (.*$)/m, "<h1>\\1</h1>")
    |> String.replace(~r/^## (.*$)/m, "<h2>\\1</h2>")
    |> String.replace(~r/^### (.*$)/m, "<h3>\\1</h3>")
    |> String.replace(~r/^#### (.*$)/m, "<h4>\\1</h4>")
    |> String.replace(~r/^##### (.*$)/m, "<h5>\\1</h5>")
    |> String.replace(~r/^###### (.*$)/m, "<h6>\\1</h6>")
    |> String.replace(~r/^> (.*$)/m, "<blockquote>\\1</blockquote>")
    |> String.replace(~r/\[([^\]]+)\]\(([^)]+)\)/, "<a href=\"\\2\">\\1</a>")
    |> String.replace(~r/!\[([^\]]*)\]\(([^)]+)\)/, "<img src=\"\\2\" alt=\"\\1\">")
    |> String.replace(~r/^[-*] (.*$)/m, "<li>\\1</li>")
    |> String.replace(~r/^\d+\. (.*$)/m, "<li>\\1</li>")
    |> String.replace(~r/\n/, "<br>")
  end
end
