defmodule L3rnDevWeb.RepositoryLive do
  use L3rnDevWeb, :live_view
  alias L3rnDevWeb.TailwindUi

  @impl true
  def render(assigns) do
    ~H"""
    <TailwindUi.breadcrumbs>
      <:hop href={~p"/repositories"}>Repositories</:hop>
      <:hop href={~p"/repositories/#{@owner}/#{@name}"}>{@repository["full_name"]}</:hop>
    </TailwindUi.breadcrumbs>
    <ul
      role="list"
      class="divide-y divide-gray-100"
      id="commits"
      phx-update="stream"
      phx-page-loading
      phx-viewport-top={elem(@pagination_links, 0) && "prev-page"}
      phx-viewport-bottom={elem(@pagination_links, 1) && "next-page"}
      class={[
        if(elem(@pagination_links, 1), do: "pb-10", else: "pb-[calc(200vh)]"),
        if(elem(@pagination_links, 0), do: "pt-10", else: "pt-[calc(200vh)]")
      ]}
    >
      <li
        :for={{commit_id, commit} <- @streams.commits}
        class="flex justify-between gap-x-6 py-5"
        id={commit_id}
      >
        <.commit commit={commit} owner={@owner} name={@name} />
      </li>
    </ul>
    """
  end

  @impl true
  def mount(%{"name" => name, "owner" => owner}, _session, socket) do
    user = socket.assigns.current_user
    {:ok, repository, _} = L3rnDev.Github.Api.get_repository(user, owner, name)
    {:ok, commits, links} = L3rnDev.Github.Api.get_commits(user, owner, name)

    socket =
      socket
      |> assign(:current_email, user.email)
      |> assign(:owner, owner)
      |> assign(:name, name)
      |> assign(:pagination_links, links)
      |> assign(:repository, repository)
      |> stream_configure(:commits, dom_id: &"commit-#{String.slice(&1["sha"], 0..6)}")
      |> stream(:commits, commits)

    {:ok, socket, layout: {L3rnDevWeb.Layouts, :main}}
  end

  @impl true
  def handle_event("prev-page", _, socket) do
    if elem(socket.assigns.pagination_links, 0) do
      {:noreply, load_commits(socket, elem(socket.assigns.pagination_links, 0), 0, 90)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("next-page", _, socket) do
    if elem(socket.assigns.pagination_links, 1) do
      {:noreply, load_commits(socket, elem(socket.assigns.pagination_links, 1))}
    else
      {:noreply, socket}
    end
  end

  defp load_commits(socket, url, at \\ -1, limit \\ -90) do
    {:ok, commits, links} =
      L3rnDev.Github.Api.get_commits(
        socket.assigns.current_user,
        socket.assigns.owner,
        socket.assigns.name,
        url
      )

    commits = if at == 0, do: Enum.reverse(commits), else: commits

    socket
    |> assign(:pagination_links, links)
    |> stream(:commits, commits, at: at, limit: limit)
  end
end
