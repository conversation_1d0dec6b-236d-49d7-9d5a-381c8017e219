defmodule L3rnDevWeb.RepositoriesLive do
  use L3rnDevWeb, :live_view

  require Ash.Query

  alias L3rnDev.Github.Repository

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash}>
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <ul
          role="list"
          class="divide-y divide-gray-100"
          id="repositories"
          phx-update="stream"
          phx-page-loading
          phx-viewport-top={@back && "prev-page"}
          phx-viewport-bottom={@forward && "next-page"}
          class={[
            if(@forward, do: "pb-10", else: "pb-[calc(200vh)]"),
            if(@back, do: "pt-10", else: "pt-[calc(200vh)]")
          ]}
        >
          <li
            :for={{repository_id, repository} <- @streams.repositories}
            class="flex items-center justify-between gap-x-6 py-5"
            id={repository_id}
          >
            <.repository
              name={repository.name}
              owner={repository.owner.login}
              full_name={repository.full_name}
              description={repository.description}
              private={repository.visibility == :private}
            />
          </li>
        </ul>
      </div>
    </Layouts.app>
    <.portal id="header" target="#page-header" class="w-full">
      <div class="mx-auto max-w-7xl px-4 py-4 sm:px-6 lg:px-8">
        <h1 class="text-lg/6 font-semibold text-gray-900">Repositories</h1>
        <.form class="grid flex-1 grid-cols-1" for={@search_form} phx-change="search">
          <input
            type="search"
            name="filter"
            aria-label="Search"
            class="col-start-1 row-start-1 block size-full bg-white pl-8 text-base text-gray-900 outline-hidden placeholder:text-gray-400 sm:text-sm/6"
            placeholder="Search"
            autocomplete="off"
          />
          <svg
            class="pointer-events-none col-start-1 row-start-1 size-5 self-center text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
            data-slot="icon"
          >
            <path
              fill-rule="evenodd"
              d="M9 3.5a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11ZM2 9a7 7 0 1 1 12.452 4.391l3.328 3.329a.75.75 0 1 1-1.06 1.06l-3.329-3.328A7 7 0 0 1 2 9Z"
              clip-rule="evenodd"
            />
          </svg>
        </.form>
      </div>
    </.portal>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_user

    socket =
      socket
      |> assign(:per_page, 20)
      |> assign(:current_email, user.email)
      |> assign(:search_form, to_form(%{"filter" => ""}))
      |> assign(:back, false)
      |> assign(:forward, false)
      |> stream_configure(:repositories, dom_id: &"repository-#{&1.id}")
      |> load_repositories()

    {:ok, socket, layout: {L3rnDevWeb.Layouts, :main}}
  end

  @impl true
  def handle_event("search", %{"filter" => filter}, socket) do
    {:noreply,
     socket
     |> assign(:search_form, to_form(%{"filter" => filter}))
     |> build_filter_form()
     |> stream(:repositories, [], reset: true)
     |> load_repositories()}
  end

  @impl true
  def handle_event("prev-page", _, socket) do
    {:noreply, load_repositories(socket, socket.assigns[:first_keyset])}
  end

  @impl true
  def handle_event("next-page", _, socket) do
    {:noreply, load_repositories(socket, nil, socket.assigns[:last_keyset])}
  end

  def build_filter_form(socket) do
    filter = socket.assigns.search_form.params["filter"]

    form =
      if filter do
        form =
          AshPhoenix.FilterForm.new(Repository)

        {form, group} = form |> AshPhoenix.FilterForm.add_group(operator: :or, return_id?: true)

        form =
          form
          |> AshPhoenix.FilterForm.add_predicate(
            :full_name,
            :contains,
            filter,
            to: group
          )

        form
        |> AshPhoenix.FilterForm.add_predicate(
          :description,
          :contains,
          filter,
          to: group
        )
      else
        nil
      end

    assign(socket, filter_form: form)
  end

  defp build_page_options(socket, before_keyset, after_keyset) do
    page_options = %{limit: socket.assigns.per_page}

    page_options =
      if after_keyset,
        do: Map.merge(page_options, %{after: after_keyset}),
        else: page_options

    if before_keyset,
      do: Map.merge(page_options, %{before: before_keyset}),
      else: page_options
  end

  def load_repositories(socket, before_keyset \\ nil, after_keyset \\ nil) do
    page_options = build_page_options(socket, before_keyset, after_keyset)

    page =
      Repository
      |> Ash.Query.for_read(:by_user_id, %{user_id: socket.assigns.current_user.id})
      |> Ash.Query.filter(archived: false)
      |> maybe_filter(socket.assigns[:filter_form])
      |> Ash.read!(load: :owner, page: Map.to_list(page_options))

    if Enum.empty?(page.results) do
      stream(socket, :repositories, [])
      |> assign(
        first_keyset: nil,
        last_keyset: nil,
        back: false,
        forward: false
      )
    else
      first_keyset =
        page.results
        |> List.first()
        |> Map.get(:__metadata__)
        |> Map.get(:keyset)

      last_keyset =
        page.results
        |> :lists.last()
        |> Map.get(:__metadata__)
        |> Map.get(:keyset)

      {repositories, at, limit} =
        if after_keyset || !before_keyset do
          {page.results, -1, socket.assigns.per_page * 3 * -1}
        else
          {Enum.reverse(page.results), 0, socket.assigns.per_page * 3}
        end

      socket
      |> stream(:repositories, repositories, at: at, limit: limit)
      |> assign(
        first_keyset: first_keyset,
        last_keyset: last_keyset,
        back: before_keyset && page.more?,
        forward: (after_keyset && page.more?) || (!after_keyset && !before_keyset && page.more?)
      )
    end
  end

  defp maybe_filter(query, nil), do: query

  defp maybe_filter(query, form), do: elem(AshPhoenix.FilterForm.filter(query, form), 1)
end
