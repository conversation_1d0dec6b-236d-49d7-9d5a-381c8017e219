defmodule L3rnDevWeb.SnippetLive do
  alias L3rnDev.Source.Snippet
  alias L3rnDevWeb.TailwindUi
  use L3rnDevWeb, :live_view

  @impl true
  def render(assigns) do
    ~H"""
    <%= if @live_action == :show do %>
      <Layouts.app flash={@flash}>
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
          <TailwindUi.breadcrumbs>
            <:hop href={~p"/snippets"}>Snippets</:hop>
            <:hop href={~p"/snippets/#{@snippet.id}"}>{@snippet.name}</:hop>
          </TailwindUi.breadcrumbs>

          <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
            <div class="-ml-4 -mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
              <div class="ml-4 mt-4">
                <h3 class="text-base font-semibold leading-6 text-gray-900">{@snippet.name}</h3>
                <%!-- <p class="mt-1 text-sm text-gray-500">
              Lorem ipsum dolor sit amet consectetur adipisicing elit quam corrupti consectetur.
            </p> --%>
              </div>
              <div class="ml-4 mt-4 flex-shrink-0">
                <%!-- <button
              type="button"
              class="relative inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              Create new job
            </button> --%>
              </div>
            </div>
          </div>
        </div>
      </Layouts.app>
    <% end %>
    <%= if @live_action == :new do %>
      <Layouts.app flash={@flash}>
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
          <.form
            :let={f}
            for={@form}
            phx-submit="create"
            phx-change="validate"
            novalidate="true"
            autocomplete="off"
          >
            <div class="grid grid-cols-6 gap-6">
              <div class="col-span-6 sm:col-span-3">
                <.input field={f[:name]} label="Snippet Name" />
              </div>
            </div>
            <div class="mt-6">
              <button
                type="submit"
                class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Create Snippet
              </button>
            </div>
          </.form>
        </div>
      </Layouts.app>
    <% end %>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    {:ok, socket}
  end

  @impl true
  def handle_params(params, _session, socket) do
    case socket.assigns.live_action do
      :show ->
        {:noreply,
         assign(socket,
           snippet: Snippet.get_by_id!(params["id"], actor: socket.assigns.current_user)
         )}

      :new ->
        {:noreply,
         assign(socket,
           form:
             AshPhoenix.Form.for_create(Snippet, :create, actor: socket.assigns.current_user)
             |> to_form()
         )}
    end
  end

  @impl true
  def handle_event("validate", %{"form" => params}, socket) do
    {:noreply,
     assign(socket,
       create_form: AshPhoenix.Form.validate(socket.assigns.form, params)
     )}
  end

  def handle_event(
        "create",
        %{"form" => params},
        socket
      ) do
    case AshPhoenix.Form.submit(socket.assigns.form, params) do
      {:ok, snippet} ->
        socket =
          socket
          |> put_flash(:info, "Created Snippet")
          |> redirect(to: ~p"/snippets/#{snippet.id}")

        {:noreply, socket}

      {:error, form} ->
        {:noreply, assign(socket, form: form)}
    end
  end
end
