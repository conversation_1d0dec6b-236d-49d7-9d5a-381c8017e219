defmodule L3rnDevWeb.SectionsLive do
  use L3rnDevWeb, :live_view

  alias L3rnDev.Books.Book
  alias L3rnDev.Books.Section

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash}>
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="border-b border-gray-200 pb-5">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Sections for {@book.title}
          </h1>
          <.link
            href={~p"/books/#{@book.id}"}
            class="mt-4 inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Back to Book
          </.link>
        </div>

        <div :if={@live_action == :new} class="mt-8">
          <h2 class="text-xl font-semibold text-gray-900">New Section</h2>
          <.form
            :let={f}
            for={@form}
            id="section-form"
            phx-change="validate"
            phx-submit="create"
            class="mt-6 space-y-6"
          >
            <.input field={f[:title]} type="text" label="Title" />
            <.input field={f[:body]} type="text" label="Body" />
            <div class="mt-6">
              <button
                type="submit"
                class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Create Section
              </button>
            </div>
          </.form>
        </div>
      </div>
    </Layouts.app>
    """
  end

  @impl true
  def mount(params, _session, socket) do
    {:ok,
     assign(socket, book: Ash.get!(Book, params["book_id"], actor: socket.assigns.current_user))}
  end

  @impl true
  def handle_params(_params, _session, socket) do
    case socket.assigns.live_action do
      :new ->
        form =
          AshPhoenix.Form.for_create(
            Section,
            :create,
            actor: socket.assigns.current_user,
            api: L3rnDev.Books
          )
          |> to_form()

        {:noreply, assign(socket, form: form)}
    end
  end

  @impl true
  def handle_event("validate", %{"form" => section_params}, socket) do
    {:noreply,
     assign(socket,
       form: AshPhoenix.Form.validate(socket.assigns.form, section_params)
     )}
  end

  @impl true
  def handle_event("create", %{"form" => section_params}, socket) do
    case AshPhoenix.Form.submit(socket.assigns.form,
           params: Map.put(section_params, :book_id, socket.assigns.book.id)
         ) do
      {:ok, section} ->
        {:noreply,
         socket
         |> put_flash(:info, "Section created successfully!")
         |> push_navigate(to: ~p"/books/#{socket.assigns.book.id}/sections/#{section.id}")}

      {:error, form} ->
        {:noreply, assign(socket, form: form)}
    end
  end
end
