defmodule L3rnDevWeb.AuthController do
  use L3rnDevWeb, :controller
  use AshAuthentication.Phoenix.Controller
  alias AshAuthentication.Jwt
  alias L3rnDev.UserFromAuth

  def success(conn, activity, user, _token) do
    return_to = get_session(conn, :return_to) || ~p"/"

    message =
      case activity do
        {:confirm_new_user, :confirm} -> "Your email address has now been confirmed"
        {:password, :reset} -> "Your password has successfully been reset"
        _ -> "You are now signed in"
      end

    conn
    |> delete_session(:return_to)
    |> store_in_session(user)
    |> assign(:current_user, user)
    |> put_flash(:info, message)
    |> redirect(to: return_to)
  end

  def failure(conn, activity, reason) do
    message =
      case {activity, reason} do
        {_,
         %AshAuthentication.Errors.AuthenticationFailed{
           caused_by: %Ash.Error.Forbidden{
             errors: [%AshAuthentication.Errors.CannotConfirmUnconfirmedUser{}]
           }
         }} ->
          """
          You have already signed in another way, but have not confirmed your account.
          You can confirm your account using the link we sent to you, or by resetting your password.
          """

        _ ->
          "Incorrect email or password"
      end

    conn
    |> put_flash(:error, message)
    |> redirect(to: ~p"/sign-in")
  end

  def sign_out(conn, _params) do
    return_to = get_session(conn, :return_to) || ~p"/"

    conn
    |> clear_session(:l3rn_dev)
    |> put_flash(:info, "You are now signed out")
    |> redirect(to: return_to)
  end

  def request(conn, _params) do
    render(conn, "auth/request.html", callback_url: Ueberauth.Strategy.Helpers.callback_url(conn))
  end

  def callback(%{assigns: %{ueberauth_failure: _fails}} = conn, _params) do
    conn
    |> put_flash(:error, "Failed to authenticate.")
    |> redirect(to: "/")
  end

  def callback(%{assigns: %{ueberauth_auth: auth}} = conn, _params) do
    case UserFromAuth.find_or_create(auth) do
      {:ok, user} ->
        # refresh repositories
        %{"user_id" => user.id} |> L3rnDev.Github.RepositoryImportWorker.new() |> Oban.insert()

        conn =
          conn
          |> Ash.PlugHelpers.set_context(%{
            actor: user
          })

        user = add_token_to_user(conn, user)

        conn
        |> put_flash(:info, "Successfully authenticated.")
        |> store_in_session(user)
        |> assign(:current_user, user)
        |> redirect(to: "/")
    end
  end

  defp add_token_to_user(conn, user) do
    context = Ash.PlugHelpers.get_context(conn)

    {:ok, token, _claims} =
      Jwt.token_for_user(
        user,
        %{"purpose" => "sign_in"},
        Ash.Context.to_opts(context,
          token_lifetime: {60, :minutes}
        ),
        context
      )

    Ash.Resource.put_metadata(user, :token, token)
  end
end
