<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <.live_title default="l3rn.dev" suffix=" · l3rn.dev">
      {assigns[:page_title]}
    </.live_title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/css/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/js/app.js"}>
    </script>
    <script>
      (() => {
        const setTheme = (theme) => {
          if (theme === "system") {
            localStorage.removeItem("phx:theme");
            document.documentElement.removeAttribute("data-theme");
          } else {
            localStorage.setItem("phx:theme", theme);
            document.documentElement.setAttribute("data-theme", theme);
          }
        };
        if (!document.documentElement.hasAttribute("data-theme")) {
          setTheme(localStorage.getItem("phx:theme") || "system");
        }
        window.addEventListener("storage", (e) => e.key === "phx:theme" && setTheme(e.newValue || "system"));
        window.addEventListener("phx:set-theme", ({ detail: { theme } }) => setTheme(theme));
      })();
    </script>
  </head>
  <body class="bg-white l3rn-dev-web">
    <div class="bg-white">
      <!-- Header -->
      <header class="absolute inset-x-0 top-0 z-50">
        <nav class="flex items-center justify-between p-6 lg:px-8" aria-label="Global">
          <div class="flex lg:flex-1">
            <a href="#" class="-m-1.5 p-1.5">
              <span class="sr-only">L3rn.Dev</span>
              <.icon name="hero-code-bracket" class="text-indigo-600 font-extrabold" />
            </a>
          </div>
          <div class="flex lg:hidden">
            <TailwindUi.show_mobile_menu_button id="mobile-menu" />
          </div>
          <div class="hidden lg:flex lg:gap-x-12">
            <a href={~p"/repositories"} class="text-sm font-semibold leading-6 text-gray-900">
              Repositories
            </a>
          </div>
          <div class="hidden lg:flex lg:flex-1 lg:justify-end">
            <ul class="relative z-10 flex items-center gap-4 px-4 sm:px-6 lg:px-8 justify-end">
              <.user_drop_down_menu :if={@current_user} current_user={@current_user} />
              <.github_login_button :if={!@current_user} />
            </ul>
          </div>
        </nav>
        <!-- Mobile menu, show/hide based on menu open state. -->
        <TailwindUi.mobile_menu id="mobile-menu">
          <:logo>
            <a href="#" class="-m-1.5 p-1.5">
              <span class="sr-only">L3rn.Dev</span>
              <.icon name="hero-code-bracket" class="text-indigo-600 font-extrabold" />
            </a>
          </:logo>
          <:sidebar>
            <div class="space-y-2 py-6">
              <a href={~p"/repositories"} class="text-sm font-semibold leading-6 text-gray-900">
                Repositories
              </a>
            </div>
            <div class="py-6">
              <.user_drop_down_menu :if={@current_user} current_user={@current_user} />
              <.github_login_button :if={!@current_user} />
            </div>
          </:sidebar>
        </TailwindUi.mobile_menu>
      </header>
      {@inner_content}
      <!-- Footer -->
      <div class="mx-auto mt-32 max-w-7xl px-6 lg:px-8">
        <footer
          aria-labelledby="footer-heading"
          class="relative border-t border-gray-900/10 py-24 sm:mt-56 sm:py-32"
        >
          <h2 id="footer-heading" class="sr-only">Footer</h2>
          <div class="xl:grid xl:grid-cols-3 xl:gap-8">
            <.icon name="hero-code-bracket" class="text-indigo-600 font-extrabold" />
            <%!-- <div class="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
              <div class="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 class="text-sm font-semibold leading-6 text-gray-900">Solutions</h3>
                  <ul role="list" class="mt-6 space-y-4">
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Hosting</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Data Services</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Uptime Monitoring</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Enterprise Services</a>
                    </li>
                  </ul>
                </div>
                <div class="mt-10 md:mt-0">
                  <h3 class="text-sm font-semibold leading-6 text-gray-900">Support</h3>
                  <ul role="list" class="mt-6 space-y-4">
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Pricing</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Documentation</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Guides</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">API Reference</a>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 class="text-sm font-semibold leading-6 text-gray-900">Company</h3>
                  <ul role="list" class="mt-6 space-y-4">
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">About</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Blog</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Jobs</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Press</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Partners</a>
                    </li>
                  </ul>
                </div>
                <div class="mt-10 md:mt-0">
                  <h3 class="text-sm font-semibold leading-6 text-gray-900">Legal</h3>
                  <ul role="list" class="mt-6 space-y-4">
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Claim</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Privacy</a>
                    </li>
                    <li>
                      <a href="#" class="text-sm leading-6 text-gray-600 hover:text-gray-900">Terms</a>
                    </li>
                  </ul>
                </div>
              </div>
            </div> --%>
          </div>
        </footer>
      </div>
    </div>
  </body>
</html>
