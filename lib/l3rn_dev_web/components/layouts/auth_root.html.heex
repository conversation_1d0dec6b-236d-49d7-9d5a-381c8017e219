<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <.live_title suffix=" · l3rn.dev">
      {assigns[:page_title]}
    </.live_title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/css/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/js/app.js"}>
    </script>
    <script>
      (() => {
        const setTheme = (theme) => {
          if (theme === "system") {
            localStorage.removeItem("phx:theme");
            document.documentElement.removeAttribute("data-theme");
          } else {
            localStorage.setItem("phx:theme", theme);
            document.documentElement.setAttribute("data-theme", theme);
          }
        };
        if (!document.documentElement.hasAttribute("data-theme")) {
          setTheme(localStorage.getItem("phx:theme") || "system");
        }
        window.addEventListener("storage", (e) => e.key === "phx:theme" && setTheme(e.newValue || "system"));
        window.addEventListener("phx:set-theme", ({ detail: { theme } }) => setTheme(theme));
      })();
    </script>
    {Application.get_env(:live_debugger, :live_debugger_tags)}
  </head>
  <body class="antialiased l3rn-dev-web h-screen bg-gray-100">
    <div class="min-h-full">
      <nav class="bg-indigo-600">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div class="flex h-16 items-center justify-between">
            <div class="flex items-center">
              <div class="shrink-0">
                <span class="sr-only">L3rn.Dev</span>
                <.icon name="hero-code-bracket" class="text-white font-extrabold" />
              </div>
              <div class="hidden md:block">
                <div class="ml-10 flex items-baseline space-x-4">
                  <TailwindUi.menu_link
                    href={~p"/dashboard"}
                    label="Dashboard"
                    current_url={@current_url}
                    base_css="rounded-md px-3 py-2 text-sm font-medium text-white"
                    active_css="bg-indigo-700 text-white"
                    inactive_css="text-white hover:bg-indigo-500/75"
                  />

                  <TailwindUi.menu_link
                    href={~p"/repositories"}
                    label="Repositories"
                    current_url={@current_url}
                    base_css="rounded-md px-3 py-2 text-sm font-medium text-white"
                    active_css="bg-indigo-700 text-white"
                    inactive_css="text-white hover:bg-indigo-500/75"
                  />

                  <TailwindUi.menu_link
                    href={~p"/snippets"}
                    label="Snippets"
                    current_url={@current_url}
                    base_css="rounded-md px-3 py-2 text-sm font-medium text-white"
                    active_css="bg-indigo-700 text-white"
                    inactive_css="text-white hover:bg-indigo-500/75"
                  />

                  <TailwindUi.menu_link
                    href={~p"/books"}
                    label="Books"
                    current_url={@current_url}
                    base_css="rounded-md px-3 py-2 text-sm font-medium text-white"
                    active_css="bg-indigo-700 text-white"
                    inactive_css="text-white hover:bg-indigo-500/75"
                  />
                </div>
              </div>
            </div>
            <div class="hidden md:block">
              <div class="ml-4 flex items-center md:ml-6">
                
<!-- Profile dropdown -->
                <div class="mr-2">
                  <.theme_toggle />
                </div>
                <.user_drop_down_menu current_user={@current_user} />
              </div>
            </div>
            <div class="-mr-2 flex md:hidden">
              <!-- Mobile menu button -->
              <TailwindUi.show_mobile_menu_button id="mobile-menu-button" />
            </div>
          </div>
        </div>
        
<!-- Mobile menu, show/hide based on menu state. -->
        <div class="md:hidden" id="mobile-menu">
          <div class="space-y-1 px-2 pt-2 pb-3 sm:px-3">
            <TailwindUi.menu_link
              href={~p"/dashboard"}
              label="Dashboard"
              current_url={@current_url}
              base_css="block rounded-md px-3 py-2 text-base font-medium text-white"
              active_css="bg-indigo-700 text-white"
              inactive_css="text-white hover:bg-indigo-500/75"
            />
            <TailwindUi.menu_link
              href={~p"/repositories"}
              label="Repositories"
              current_url={@current_url}
              base_css="block rounded-md px-3 py-2 text-base font-medium text-white"
              active_css="bg-indigo-700 text-white"
              inactive_css="text-white hover:bg-indigo-500/75"
            />
            <TailwindUi.menu_link
              href={~p"/snippets"}
              label="Snippets"
              current_url={@current_url}
              base_css="block rounded-md px-3 py-2 text-base font-medium text-white"
              active_css="bg-indigo-700 text-white"
              inactive_css="text-white hover:bg-indigo-500/75"
            />
            <TailwindUi.menu_link
              href={~p"/books"}
              label="Books"
              current_url={@current_url}
              base_css="block rounded-md px-3 py-2 text-base font-medium text-white"
              active_css="bg-indigo-700 text-white"
              inactive_css="text-white hover:bg-indigo-500/75"
            />
          </div>
          <div class="border-t border-indigo-700 pt-4 pb-3">
            <div class="flex items-center px-5">
              <div class="shrink-0">
                <img class="size-10 rounded-full" src={@current_user.avatar} alt="" />
              </div>
              <div class="ml-3">
                <div class="text-base font-medium text-white">
                  {@current_user.github_nickname}
                </div>
              </div>
            </div>
            <div class="mt-3 space-y-1 px-2">
              <a
                href="#"
                class="block rounded-md px-3 py-2 text-base font-medium text-white hover:bg-indigo-500/75"
              >
                Your Profile
              </a>
              <a
                href="#"
                class="block rounded-md px-3 py-2 text-base font-medium text-white hover:bg-indigo-500/75"
              >
                Settings
              </a>
              <a
                href="#"
                class="block rounded-md px-3 py-2 text-base font-medium text-white hover:bg-indigo-500/75"
              >
                Sign out
              </a>
            </div>
          </div>
        </div>
      </nav>

      {@inner_content}
    </div>
    <div id="drawer"></div>
  </body>
</html>
