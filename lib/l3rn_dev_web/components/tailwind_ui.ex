defmodule L3rnDevWeb.TailwindUi do
  @moduledoc """
  Provides Tailwind UI components.
  """
  use Phoenix.Component
  alias Phoenix.LiveView.JS
  import L3rnDevWeb.CoreComponents, only: [icon: 1]

  @doc """
  Render menu link.
  """
  attr :href, :string, required: true
  attr :label, :string, required: true
  attr :current_url, :string, required: true
  attr :base_css, :string, required: true
  attr :active_css, :string, required: true
  attr :inactive_css, :string, required: true

  def menu_link(assigns) do
    ~H"""
    <a
      href={@href}
      class={[
        @base_css,
        String.starts_with?(@current_url, @href) && @active_css,
        !String.starts_with?(@current_url, @href) && @inactive_css
      ]}
      aria-current={if String.starts_with?(@current_url, @href), do: "page"}
    >
      {@label}
    </a>
    """
  end

  @doc """
  Render Tailwind UI Badge Component.
  """
  attr :label, :string, required: true

  attr :color, :string,
    default: "gray",
    values: ~w(gray red yellow green blue purple indigo pink)

  def badge(assigns) do
    ~H"""
    <span class={[
      "inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium",
      @color == "gray" && "bg-gray-50 text-gray-700",
      @color == "red" && "bg-red-100 text-red-700",
      @color == "yellow" && "bg-yellow-100 text-yellow-700",
      @color == "green" && "bg-green-100 text-green-700",
      @color == "blue" && "bg-blue-100 text-blue-700",
      @color == "purple" && "bg-purple-100 text-purple-700",
      @color == "indigo" && "bg-indigo-100 text-indigo-700",
      @color == "pink" && "bg-pink-100 text-pink-700"
    ]}>
      {@label}
    </span>
    """
  end

  @doc """
  Render Tailwind UI Dropdown Component.
  """

  def show_dropdown(to) do
    JS.show(
      to: to,
      transition:
        {"transition ease-out duration-100", "transform opacity-0 scale-95",
         "transform opacity-100 scale-100"}
    )
    |> JS.set_attribute({"aria-expanded", "true"}, to: to)
  end

  def hide_dropdown(to) do
    JS.hide(
      to: to,
      transition:
        {"transition ease-in duration-75", "transform opacity-100 scale-100",
         "transform opacity-0 scale-95"}
    )
    |> JS.remove_attribute("aria-expanded", to: to)
  end

  attr :id, :string, required: true

  attr :title, :string

  slot :button_content

  slot :link do
    attr :navigate, :string
    attr :href, :string
    attr :method, :any
  end

  def dropdown(assigns) do
    ~H"""
    <button
      type="button"
      class="flex items-center text-gray-700 hover:text-gray-900"
      id="options-menu-0-button"
      aria-expanded="false"
      aria-haspopup="true"
      phx-click={show_dropdown("##{@id}-dropdown")}
    >
      <%= if @button_content != [] do %>
        {render_slot(@button_content)}
      <% else %>
        <span class="sr-only">Open {@title}</span>
        <svg class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path d="M10 3a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM10 8.5a1.5 1.5 0 110 3 1.5 1.5 0 010-3zM11.5 15.5a1.5 1.5 0 10-3 0 1.5 1.5 0 003 0z" />
        </svg>
      <% end %>
    </button>

    <div
      class="hidden absolute right-0 z-10 mt-2 min-w-[180px] origin-top-right rounded-md bg-white py-1 shadow-lg border border-gray-100 focus:outline-none"
      role="menu"
      aria-orientation="vertical"
      aria-labelledby="options-menu-0-button"
      tabindex="-1"
      id={"#{@id}-dropdown"}
      phx-click-away={hide_dropdown("##{@id}-dropdown")}
    >
      <%= for link <- @link do %>
        <.link
          tabindex="-1"
          role="menuitem"
          class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
          {link}
        >
          {render_slot(link)}
        </.link>
      <% end %>
    </div>
    """
  end

  slot :hop do
    attr :href, :string
  end

  def breadcrumbs(assigns) do
    ~H"""
    <nav class="flex mb-3" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-2">
        <li>
          <div>
            <a href="/" class="text-gray-500 hover:text-blue-600">
              <svg
                class="h-4 w-4 flex-shrink-0"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M9.293 2.293a1 1 0 011.414 0l7 7A1 1 0 0117 11h-1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-3a1 1 0 00-1-1H9a1 1 0 00-1 1v3a1 1 0 01-1 1H5a1 1 0 01-1-1v-6H3a1 1 0 01-.707-1.707l7-7z"
                  clip-rule="evenodd"
                />
              </svg>
              <span class="sr-only">Home</span>
            </a>
          </div>
        </li>
        <%= for hop <- @hop do %>
          <li>
            <div class="flex items-center">
              <svg
                class="h-3 w-3 flex-shrink-0 text-gray-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                  clip-rule="evenodd"
                />
              </svg>
              <.link class="ml-2 text-sm font-medium text-gray-600 hover:text-blue-600" {hop}>
                {render_slot(hop)}
              </.link>
            </div>
          </li>
        <% end %>
      </ol>
    </nav>
    """
  end

  @doc """
  Render Tailwind UI Mobile Menu.
  """

  def hide_mobile_menu(to) do
    JS.transition(
      {"ease-linear duration-300", "opacity-100", "opacity-0"},
      to: to
    )
    |> JS.add_class("hidden", to: to)
  end

  def show_mobile_menu(to) do
    JS.transition(
      {"ease-linear duration-300", "opacity-0", "opacity-100"},
      to: to
    )
    |> JS.remove_class("hidden", to: to)
  end

  def toggle_mobile_menu(to) do
    JS.toggle(
      to: to,
      in: {"ease-linear duration-300", "opacity-0", "opacity-100"},
      out: {"ease-linear duration-300", "opacity-100", "opacity-0"}
    )
  end

  attr :id, :string, required: true

  slot :logo
  slot :sidebar

  def mobile_menu(assigns) do
    ~H"""
    <div class="lg:hidden" role="dialog" aria-modal="true" id={@id}>
      <div class="fixed inset-0 z-50 bg-gray-800/30"></div>
      <div class="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-4 py-4 sm:max-w-sm sm:border-l sm:border-gray-200">
        <div class="flex items-center justify-between">
          {render_slot(@logo)}
          <button
            type="button"
            class="p-2 text-gray-600 hover:text-gray-900"
            phx-click={hide_mobile_menu("##{@id}")}
          >
            <span class="sr-only">Close menu</span>
            <svg
              class="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="mt-4 flow-root">
          <div class="divide-y divide-gray-200">
            {render_slot(@sidebar)}
          </div>
        </div>
      </div>
    </div>
    """
  end

  attr :id, :string, required: true

  def show_mobile_menu_button(assigns) do
    ~H"""
    <button
      type="button"
      class="relative inline-flex items-center justify-center rounded-md bg-indigo-600 p-2 text-indigo-200 hover:bg-indigo-500/75 hover:text-white focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-indigo-600 focus:outline-hidden"
      phx-click={toggle_mobile_menu("##{@id}")}
    >
      <span class="sr-only">Open main menu</span>
      <svg
        class="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        aria-hidden="true"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
        />
      </svg>
    </button>
    """
  end

  @doc """
  Renders a drawer component.

  ## Examples

      <.drawer id="confirm-drawer">
        This is a drawer.
      </.drawer>

  JS commands may be passed to show or hide the drawer:

      <.button phx-click={show_drawer("confirm-drawer")}>
        Show drawer
      </.button>

      <.button phx-click={hide_drawer("confirm-drawer")}>
        Hide drawer
      </.button>
  """
  attr :id, :string, required: true
  attr :show, :boolean, default: false
  attr :on_cancel, JS, default: %JS{}
  attr :position, :string, default: "right", values: ~w(left right)
  attr :title, :string, default: nil
  attr :subtitle, :string, default: nil
  attr :form, :any, default: nil

  slot :inner_block, required: true

  def drawer(assigns) do
    ~H"""
    <div
      id={@id}
      phx-mounted={@show && show_drawer(@id)}
      phx-remove={hide_drawer(@id)}
      data-cancel={JS.exec(@on_cancel, "phx-remove")}
      class="relative z-50 hidden"
      role="dialog"
      aria-modal="true"
    >
      <div
        id={"#{@id}-bg"}
        class="fixed inset-0 bg-gray-800/60 transition-opacity"
        aria-hidden="true"
      />

      <div class="fixed inset-0 overflow-hidden">
        <div class="absolute inset-0 overflow-hidden">
          <div class={"pointer-events-none fixed inset-y-0 #{@position == "right" && "right-0 pl-10 sm:pl-16" || "left-0 pr-10 sm:pr-16"} flex max-w-full"}>
            <div class="pointer-events-auto w-screen max-w-md">
              <.form
                id={"#{@id}-container"}
                for={@form}
                phx-click-away={JS.exec("data-cancel", to: "##{@id}")}
                class="flex h-full flex-col bg-white shadow-xl"
                phx-change="validate"
                phx-submit="submit"
              >
                <div class="h-full flex-1 overflow-y-auto">
                  <div class="bg-indigo-700 px-4 py-6 sm:px-6 sticky top-0 z-20">
                    <div class="flex items-center justify-between">
                      <h2 class="text-base font-semibold text-white" id={"#{@id}-title"}>
                        {@title}
                      </h2>
                      <div class="ml-3 flex h-7 items-center">
                        <button
                          phx-click={JS.exec("data-cancel", to: "##{@id}")}
                          type="button"
                          class="relative rounded-md bg-indigo-700 text-indigo-200 hover:text-white focus-visible:ring-2 focus-visible:ring-white focus-visible:outline-hidden"
                          aria-label="close"
                        >
                          <span class="absolute -inset-2.5"></span>
                          <span class="sr-only">Close panel</span>
                          <.icon name="hero-x-mark-solid" class="size-6" />
                        </button>
                      </div>
                    </div>
                    <div :if={@subtitle} class="mt-1">
                      <p class="text-sm text-indigo-300">
                        {@subtitle}
                      </p>
                    </div>
                  </div>
                  <div class="flex flex-1 flex-col justify-between">
                    <div id={"#{@id}-content"} class="divide-y divide-gray-200 px-4 sm:px-6">
                      <div class="space-y-6 pt-6 pb-5">
                        {render_slot(@inner_block)}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex shrink-0 justify-end px-4 py-4 sticky bottom-0 z-20 bg-white border-t border-gray-200">
                  <button
                    phx-click={JS.exec("data-cancel", to: "##{@id}")}
                    type="button"
                    class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-xs ring-1 ring-gray-300 ring-inset hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    class="ml-4 inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  >
                    Save
                  </button>
                </div>
              </.form>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Shows the drawer with the given ID.

  ## Examples

      <.button phx-click={show_drawer("settings-drawer")}>
        Show drawer
      </.button>
  """
  def show_drawer(js \\ %JS{}, id) when is_binary(id) do
    js
    |> JS.show(to: "##{id}")
    |> JS.show(
      to: "##{id}-bg",
      time: 300,
      transition: {"transition-all transform ease-out duration-300", "opacity-0", "opacity-100"}
    )
    |> JS.show(
      to: "##{id}-container",
      time: 500,
      transition: {
        "transform transition ease-in-out duration-500 sm:duration-700",
        "translate-x-full",
        "translate-x-0"
      }
    )
    |> JS.add_class("overflow-hidden", to: "body")
    |> JS.focus_first(to: "##{id}-content")
  end

  @doc """
  Hides the drawer with the given ID.

  ## Examples

      <.button phx-click={hide_drawer("settings-drawer")}>
        Hide drawer
      </.button>
  """
  def hide_drawer(js \\ %JS{}, id) do
    js
    |> JS.hide(
      to: "##{id}-bg",
      transition: {"transition-all transform ease-in duration-200", "opacity-100", "opacity-0"}
    )
    |> JS.hide(
      to: "##{id}-container",
      time: 500,
      transition: {
        "transform transition ease-in-out duration-500 sm:duration-700",
        "translate-x-0",
        "translate-x-full"
      }
    )
    |> JS.hide(to: "##{id}", transition: {"block", "block", "hidden"})
    |> JS.remove_class("overflow-hidden", to: "body")
    |> JS.pop_focus()
  end
end
