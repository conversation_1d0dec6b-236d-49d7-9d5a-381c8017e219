defmodule L3rnDevWeb.Components do
  @moduledoc """
  Main app components.
  """

  use Phoenix.Component
  use L3rnDevWeb, :verified_routes
  alias L3rnDevWeb.TailwindUi
  import L3rnDevWeb.CoreComponents
  alias Phoenix.LiveView.ColocatedHook

  @doc """
  Returns repository lisiting item.

  Accepts the following attributes:

    * `:name` - Repository name to display.
    * `:description` - Repository description.
    * `:private` - Private Repository.

  ## Examples

     <.repository name="test repo" description="Description" private="true" />
  """
  attr :name, :atom, required: true
  attr :owner, :string, required: true
  attr :full_name, :string, required: true
  attr :description, :string, required: true
  attr :private, :boolean, default: false

  def repository(assigns) do
    ~H"""
    <div class="min-w-0">
      <div class="flex items-center gap-x-3">
        <p class="text-base font-medium text-gray-900">
          <a href={~p"/repositories/#{@owner}/#{@name}"} class="hover:underline hover:text-blue-600">
            {@full_name}
          </a>
        </p>
        <span
          :if={!@private}
          class="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800"
        >
          Public
        </span>
        <span
          :if={@private}
          class="inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium bg-red-100 text-gray-800"
        >
          Private
        </span>
      </div>
      <div class="mt-1 text-sm text-gray-600">
        {@description}
      </div>
    </div>
    <div class="flex flex-none items-center gap-x-4">
      <a
        href={~p"/repositories/#{@owner}/#{@name}"}
        class="hidden rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:block"
      >
        View project<span class="sr-only">, {@full_name}</span>
      </a>
      <%!-- <div class="relative flex-none">
          <TailwindUi.dropdown id={"repo-#{@id}"} title="options">
            <:link href="#">Edit<span class="sr-only">, <%= @full_name %></span></:link>
            <:link href="#">Move<span class="sr-only">, <%= @full_name %></span></:link>
            <:link href="#">Delete<span class="sr-only">, <%= @full_name %></span></:link>
          </TailwindUi.dropdown>
        </div> --%>
    </div>
    """
  end

  @doc """
  Returns commit listing.

  Accepts the following attributes:

    * `:commit` - Commit data.

  ## Examples

     <.commit commit={commit} />
  """
  attr :commit, :any, required: true
  attr :owner, :string, required: true
  attr :name, :string, required: true

  def commit(assigns) do
    assigns = assign_new(assigns, :sha, fn -> String.slice(assigns.commit["sha"], 0..6) end)

    ~H"""
    <div class="flex min-w-0 gap-x-4">
      <div class="min-w-0 flex-auto">
        <a
          href={~p"/repositories/#{@owner}/#{@name}/#{@sha}"}
          class="text-sm font-medium text-gray-900 hover:text-blue-600 hover:underline"
        >
          {@commit["commit"]["message"]}
        </a>
      </div>
    </div>
    <div class="hidden shrink-0 sm:flex sm:flex-col sm:items-end">
      <p class="text-xs font-mono text-gray-600 bg-gray-50 px-2 py-0.5 rounded">{@sha}</p>
      <p class="mt-1 text-xs text-gray-500">
        {@commit["author"]["login"]} committed
        <time
          datetime={@commit["commit"]["committer"]["date"]}
          class="text-gray-600 whitespace-nowrap"
        >
          {date_ago(@commit["commit"]["committer"]["date"])}
        </time>
      </p>
    </div>
    """
  end

  def date_ago(date) do
    {:ok, date} = Timex.parse(date, "{RFC3339z}")
    Timex.from_now(date)
  end

  @doc """
  Returns GitDiff.Patch markup.

    * `:patch` - GitDiff.Patch.

  ## Examples

     <.patch patch="..." />
  """
  attr :patch, GitDiff.Patch, required: true
  attr :selected_lines, :map, default: %{}

  def patch(assigns) do
    {label, color} =
      case assigns.patch do
        %{from: nil, to: _to} -> {"created", "green"}
        %{from: _from, to: nil} -> {"deleted", "red"}
        %{from: from, to: to} when from == to -> {"changed", "yellow"}
        %{from: from, to: to} when from != to -> {"renamed", "blue"}
        _ -> nil
      end

    assigns = assign(assigns, label: label, color: color)

    ~H"""
    <div class="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow-sm my-4">
      <div class="px-4 py-5 sm:px-6">
        <p class="text-sm font-medium text-gray-800 font-mono pt-2">
          {@patch.from}
          <span :if={@patch.from != @patch.to} class="flex items-center gap-x-1 inline-flex">
            <.icon :if={@patch.from} name="hero-arrow-long-right" class="h-3 w-3 text-gray-500" />
            {@patch.to}
          </span>
          <TailwindUi.badge label={@label} color={@color} />
        </p>
      </div>
      <.chunk
        :for={chunk <- @patch.chunks}
        chunk={chunk}
        file={@patch.to || @patch.from}
        selected_lines={@selected_lines[@patch.to || @patch.from] || []}
      />
    </div>
    """
  end

  @doc """
  Returns GitDiff.Chink markup.

    * `:chunk` - GitDiff.Chunk.

  ## Examples

     <.chunk chunk="..." />
  """
  attr :chunk, GitDiff.Chunk, required: true
  attr :file, :string
  attr :selected_lines, :list, default: []

  def chunk(assigns) do
    file_ext =
      if assigns.file, do: Path.extname(assigns.file) |> String.trim_leading("."), else: nil

    assigns = assigns |> assign(:language, get_language_from_file_extension(file_ext))

    ~H"""
    <div class="w-full overflow-x-hidden !text-xs/2 !font-mono text-gray-900">
      <.line
        :for={line <- @chunk.lines}
        line={line}
        file={@file}
        language={@language}
        selected_lines={@selected_lines}
      />
    </div>
    """
  end

  @doc """
  Returns GitDiff.Line markup.

    * `:line` - GitDiff.Line.

  ## Examples

     <.line line="..." />
  """
  attr :line, GitDiff.Line, required: true
  attr :file, :string
  attr :language, :string
  attr :selected_lines, :list, default: []

  def line(assigns) do
    # Extract file extension for language detection

    # Map common extensions to highlight.js language names

    code_text = String.replace(assigns.line.text, ~r/(^[+|-])|\r|\n/, "")
    assigns = assigns |> assign(code_text: code_text)

    ~H"""
    <div
      class={[
        "whitespace-pre flex flex-row relative cursor-pointer items-center",
        @line.type == :add && "bg-green-100 hover:bg-green-50",
        @line.type == :remove && "bg-red-100 hover:bg-red-50"
      ]}
      phx-click="select_line"
      phx-value-from-line={@line.from_line_number}
      phx-value-to-line={@line.to_line_number}
      phx-value-file={@file}
      id={"line-#{@file}-#{@line.from_line_number}-#{@line.to_line_number}"}
      phx-hook=".SyntaxHighlighter"
    >
      <div
        :if={Enum.member?(@selected_lines, %{from: @line.from_line_number, to: @line.to_line_number})}
        class="absolute z-10 -left-6 select-none"
      >
        <.icon name="hero-check-circle-solid" class="h-4 w-4 text-blue-500" />
      </div>
      <div class="w-12 select-none flex justify-center items-center text-gray-500 select-none">
        {@line.from_line_number}
      </div>
      <div class="w-12 select-none flex justify-center items-center text-gray-500 select-none">
        {@line.to_line_number}
      </div>
      <div class="w-full overflow-x-scroll overflow-y-hidden px-2 line-wrapper">
        <code
          class={"whitespace-pre #{if @language, do: "language-#{@language}"}"}
          id={"line-#{@file}-#{@line.from_line_number}-#{@line.to_line_number}-code"}
          phx-update="ignore"
        >
          {@code_text}
        </code>
      </div>
    </div>
    <script :type={ColocatedHook} name=".SyntaxHighlighter">
      export default {
        mounted() {
          window.hljs.highlightElement(this.el.querySelector("code"));
        }
      }
    </script>
    """
  end

  @doc """
  User DropDown Menu.
  """
  attr :current_user, :any, required: true

  def user_drop_down_menu(assigns) do
    ~H"""
    <div class="relative">
      <TailwindUi.dropdown id="user-menu" title="user menu">
        <:button_content>
          <span class="sr-only">Open user menu</span>
          <img
            class="h-8 w-8 rounded-full bg-gray-50 border border-gray-200"
            src={@current_user.avatar}
            alt=""
          />
          <span class="hidden lg:flex lg:items-center">
            <span class="ml-2 text-sm font-medium text-white" aria-hidden="true">
              {@current_user.github_nickname}
            </span>
            <svg
              class="ml-1 h-4 w-4 text-white"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                clip-rule="evenodd"
              />
            </svg>
          </span>
        </:button_content>
        <:link navigate={profile_path(@current_user)}>Your profile</:link>
        <:link href={~p"/sign-out"} method="delete">Sign out</:link>
      </TailwindUi.dropdown>
    </div>
    """
  end

  @doc """
  Github Login Button.

  ## Examples

    <.github_login_button />
  """
  def github_login_button(assigns) do
    ~H"""
    <a
      href={~p"/auth/github"}
      class="inline-flex items-center gap-x-2 rounded-md bg-gray-900 px-3 py-1.5 text-sm font-medium text-white hover:bg-gray-800 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
    >
      Login with GitHub
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 98 96">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M48.854 0C21.839 0 0 22 0 49.217c0 21.756 13.993 40.172 33.405 46.69 2.427.49 3.316-1.059 3.316-2.362 0-1.141-.08-5.052-.08-9.127-13.59 2.934-16.42-5.867-16.42-5.867-2.184-5.704-5.42-7.17-5.42-7.17-4.448-3.015.324-3.015.324-3.015 4.934.326 7.523 5.052 7.523 5.052 4.367 7.496 11.404 5.378 14.235 4.074.404-3.178 1.699-5.378 3.074-6.6-10.839-1.141-22.243-5.378-22.243-24.283 0-5.378 1.94-9.778 5.014-13.2-.485-1.222-2.184-6.275.486-13.038 0 0 4.125-1.304 13.426 5.052a46.97 46.97 0 0 1 12.214-1.63c4.125 0 8.33.571 12.213 1.63 9.302-6.356 13.427-5.052 13.427-5.052 2.67 6.763.97 11.816.485 13.038 3.155 3.422 5.015 7.822 5.015 13.2 0 18.905-11.404 23.06-22.324 24.283 1.78 1.548 3.316 4.481 3.316 9.126 0 6.6-.08 11.897-.08 13.526 0 1.304.89 2.853 3.316 2.364 19.412-6.52 33.405-24.935 33.405-46.691C97.707 22 75.788 0 48.854 0z"
          fill="white"
        />
      </svg>
    </a>
    """
  end

  defp get_language_from_file_extension(file_ext) do
    case file_ext do
      # JavaScript / TypeScript
      "js" -> "javascript"
      "jsx" -> "javascript"
      "ts" -> "typescript"
      "tsx" -> "typescript"
      "mjs" -> "javascript"
      "cjs" -> "javascript"
      # Python
      "py" -> "python"
      "pyw" -> "python"
      "ipynb" -> "python"
      # Ruby
      "rb" -> "ruby"
      "erb" -> "ruby"
      "gemspec" -> "ruby"
      "rake" -> "ruby"
      # Elixir
      "ex" -> "elixir"
      "exs" -> "elixir"
      "heex" -> "elixir"
      "leex" -> "elixir"
      # CSS and styling
      "css" -> "css"
      "scss" -> "scss"
      "sass" -> "scss"
      "less" -> "less"
      # HTML / Markup
      "html" -> "html"
      "htm" -> "html"
      "xhtml" -> "html"
      "xml" -> "xml"
      "svg" -> "xml"
      # Data formats
      "json" -> "json"
      "yml" -> "yaml"
      "yaml" -> "yaml"
      # Documentation
      "md" -> "markdown"
      "markdown" -> "markdown"
      # Shell scripts
      "sh" -> "bash"
      "bash" -> "bash"
      "zsh" -> "bash"
      "ps1" -> "powershell"
      # JVM languages
      "java" -> "java"
      "gradle" -> "java"
      "kt" -> "kotlin"
      "kts" -> "kotlin"
      "scala" -> "scala"
      "groovy" -> "groovy"
      # C-family languages
      "c" -> "cpp"
      "cpp" -> "cpp"
      "cc" -> "cpp"
      "h" -> "cpp"
      "hpp" -> "cpp"
      "cs" -> "csharp"
      "swift" -> "swift"
      "m" -> "objectivec"
      "mm" -> "objectivec"
      # Other mainstream languages
      "rs" -> "rust"
      "go" -> "go"
      "php" -> "php"
      "dart" -> "dart"
      "lua" -> "lua"
      "pl" -> "perl"
      "pm" -> "perl"
      # Database
      "sql" -> "sql"
      # Config files
      "toml" -> "ini"
      "ini" -> "ini"
      "conf" -> "ini"
      "config" -> "ini"
      "properties" -> "properties"
      "prop" -> "properties"
      "gitignore" -> "bash"
      "dockerignore" -> "bash"
      "dockerfile" -> "dockerfile"
      "Dockerfile" -> "dockerfile"
      _ -> nil
    end
  end
end
