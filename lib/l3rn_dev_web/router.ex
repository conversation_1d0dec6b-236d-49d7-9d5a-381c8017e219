defmodule L3rnDevWeb.Router do
  use L3rnDevWeb, :router
  use AshAuthentication.Phoenix.Router

  import AshAuthentication.Plug.Helpers
  import Oban.Web.Router
  import PhoenixStorybook.Router

  pipeline :mcp do
    plug AshAuthentication.Strategy.ApiKey.Plug,
      resource: L3rnDev.Accounts.User,
      # Use `required?: false` to allow unauthenticated
      # users to connect, for example if some tools
      # are publicly accessible.
      required?: true
  end

  pipeline :browser do
    plug Ueberauth
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {L3rnDevWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :load_from_session
  end

  pipeline :api do
    plug :accepts, ["json"]
    plug :load_from_bearer
    plug :set_actor, :user

    plug AshAuthentication.Strategy.ApiKey.Plug,
      resource: L3rnDev.Accounts.User,
      # if you want to require an api key to be supplied, set `required?` to true
      required?: false
  end

  pipeline :sse do
    plug :accepts, ["sse"]
  end

  scope "/", L3rnDevWeb do
    pipe_through :browser

    ash_authentication_live_session :authenticated_routes do
      # in each liveview, add one of the following at the top of the module:
      #
      # If an authenticated user must be present:
      # on_mount {L3rnDevWeb.LiveUserAuth, :live_user_required}
      #
      # If an authenticated user *may* be present:
      # on_mount {L3rnDevWeb.LiveUserAuth, :live_user_optional}
      #
      # If an authenticated user must *not* be present:
      # on_mount {L3rnDevWeb.LiveUserAuth, :live_no_user}
    end
  end

  scope "/mcp" do
    pipe_through :mcp

    forward "/", AshAi.Mcp.Router,
      tools: [
        # list your tools here
        # :tool1,
        # :tool2,
        # For many tools, you will need to set the `protocol_version_statement` to the older version.
      ],
      protocol_version_statement: "2024-11-05",
      otp_app: :l3rn_dev
  end

  scope "/" do
    storybook_assets()
  end

  scope "/", L3rnDevWeb do
    pipe_through(:browser)
    live_storybook("/storybook", backend_module: L3rnDevWeb.Storybook)
    auth_routes AuthController, L3rnDev.Accounts.User, path: "/ashauth"
    sign_out_route AuthController

    # Remove these if you'd like to use your own authentication views
    sign_in_route register_path: "/register",
                  reset_path: "/reset",
                  auth_routes_prefix: "/ashauth",
                  on_mount: [{L3rnDevWeb.LiveUserAuth, :live_no_user}],
                  overrides: [
                    L3rnDevWeb.AuthOverrides,
                    AshAuthentication.Phoenix.Overrides.Default
                  ]

    # Remove this if you do not want to use the reset password feature
    reset_route auth_routes_prefix: "/ashauth",
                overrides: [L3rnDevWeb.AuthOverrides, AshAuthentication.Phoenix.Overrides.Default]

    # Remove this if you do not use the confirmation strategy
    confirm_route L3rnDev.Accounts.User, :confirm_new_user,
      auth_routes_prefix: "/ashauth",
      overrides: [L3rnDevWeb.AuthOverrides, AshAuthentication.Phoenix.Overrides.Default]

    # Remove this if you do not use the magic link strategy.
    magic_sign_in_route(L3rnDev.Accounts.User, :magic_link,
      auth_routes_prefix: "/ashauth",
      overrides: [L3rnDevWeb.AuthOverrides, AshAuthentication.Phoenix.Overrides.Default]
    )
  end

  scope "/", L3rnDevWeb do
    pipe_through :browser

    get "/", PageController, :home
  end

  # Other scopes may use custom stacks.
  # scope "/api", L3rnDevWeb do
  #   pipe_through :api
  # end

  scope "/" do
    pipe_through :sse
    get "/sse", SSE.ConnectionPlug, :call

    pipe_through :api
    post "/message", SSE.ConnectionPlug, :call
  end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:l3rn_dev, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through :browser

      live_dashboard "/dashboard", metrics: L3rnDevWeb.Telemetry
      forward "/mailbox", Plug.Swoosh.MailboxPreview
    end

    scope "/" do
      pipe_through :browser

      oban_dashboard("/oban")
    end
  end

  scope "/auth", L3rnDevWeb do
    pipe_through [:browser]

    get "/:provider", AuthController, :request
    get "/:provider/callback", AuthController, :callback
  end

  scope "/", L3rnDevWeb do
    pipe_through [:browser]

    ash_authentication_live_session :authentication_required,
      root_layout: {L3rnDevWeb.Layouts, :auth_root},
      on_mount: [{L3rnDevWeb.LiveUserAuth, :live_user_required}, L3rnDevWeb.CurrentUrl] do
      live "/dashboard", DashboardLive, :index
      live "/snippets", SnippetsLive, :index
      live "/snippets/new", SnippetLive, :new
      live "/snippets/:id", SnippetLive, :show
      live "/repositories", RepositoriesLive, :index
      live "/repositories/:owner/:name", RepositoryLive, :show
      live "/repositories/:owner/:name/:sha", CommitLive, :show
      live "/books", BooksLive, :index
      live "/books/new", BooksLive, :new
      live "/books/:id", BookLive, :show
      live "/books/:id/edit", BookLive, :edit
      live "/books/:book_id/sections", SectionsLive, :index
      live "/books/:book_id/sections/new", SectionsLive, :new
      live "/books/:book_id/sections/:id", SectionLive, :show
      live "/books/:book_id/sections/:id/edit", SectionEditLive, :edit
    end
  end
end
