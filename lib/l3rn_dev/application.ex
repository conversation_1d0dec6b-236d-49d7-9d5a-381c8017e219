defmodule L3rnDev.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    :logger.add_handler(:my_sentry_handler, Sentry.LoggerHandler, %{
      config: %{metadata: [:file, :line]}
    })

    unless Mix.env() == :prod do
      Dotenv.load()
      Mix.Task.run("loadconfig")
    end

    children = [
      # Start the Telemetry supervisor
      L3rnDevWeb.Telemetry,
      # Start the Ecto repository
      L3rnDev.Repo,
      {DNSCluster, query: Application.get_env(:l3rn_dev, :dns_cluster_query) || :ignore},
      {Oban,
       AshOban.config(
         Application.fetch_env!(:l3rn_dev, :ash_domains),
         Application.fetch_env!(:l3rn_dev, Oban)
       )},
      # Start the PubSub system
      # Start the Finch HTTP client for sending emails
      # Start a worker by calling: L3rnDev.Worker.start_link(arg)
      # {L3rnDev.Worker, arg},
      # Start to serve requests, typically the last entry
      {Phoenix.PubSub, name: L3rnDev.PubSub},
      L3rnDevWeb.Endpoint,
      {AshAuthentication.Supervisor, [otp_app: :l3rn_dev]}
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: L3rnDev.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    L3rnDevWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
