defmodule L3rnDev.Books.Section do
  use Ash.Resource,
    domain: L3rnDev.Books,
    authorizers: [Ash.Policy.Authorizer],
    data_layer: AshPostgres.DataLayer

  postgres do
    table "sections"
    repo L3rnDev.Repo
  end

  actions do
    defaults [:read]

    create :create do
      accept [:body, :title, :book_id]
    end

    update :update do
      accept [:body, :title]
    end

    read :by_id do
      argument :id, :uuid, allow_nil?: false

      get? true

      filter expr(id == ^arg(:id))
    end
  end

  policies do
    policy action_type(:create) do
      authorize_if L3rnDev.Checks.CreateSectionCheck
    end

    policy action_type(:read) do
      authorize_if relates_to_actor_via([:book, :user])
    end

    policy action_type([:update, :destroy]) do
      authorize_if relates_to_actor_via([:book, :user])
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string, allow_nil?: false
    attribute :body, :string, allow_nil?: false
  end

  relationships do
    belongs_to :book, L3rnDev.Books.Book do
      allow_nil? false
    end
  end
end
