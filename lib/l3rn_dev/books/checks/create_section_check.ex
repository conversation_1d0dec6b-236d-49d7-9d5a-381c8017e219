defmodule L3rnDev.Checks.CreateSectionCheck do
  @moduledoc """
  Policy for Section creation
  """

  use Ash.Policy.SimpleCheck
  alias L3rnDev.Books.Book

  def describe(_opts) do
    "Check if user can create a section"
  end

  def match?(actor, params, _opts) do
    case Ash.get(Book, params.changeset.attributes.book_id, actor: actor) do
      {:ok, _book} -> true
      {:error, _} -> false
    end
  end
end
