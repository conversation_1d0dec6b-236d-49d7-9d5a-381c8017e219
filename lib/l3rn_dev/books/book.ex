defmodule L3rnDev.Books.Book do
  use Ash.Resource,
    domain: L3rnDev.Books,
    authorizers: [Ash.Policy.Authorizer],
    data_layer: AshPostgres.DataLayer

  postgres do
    table "books"
    repo L3rnDev.Repo
  end

  actions do
    defaults [:read]

    create :create do
      accept [:title]

      change relate_actor(:user)
    end

    read :by_id do
      argument :id, :uuid, allow_nil?: false

      get? true

      filter expr(id == ^arg(:id))
    end
  end

  policies do
    policy action_type(:create) do
      authorize_if actor_present()
    end

    policy action_type(:read) do
      authorize_if relates_to_actor_via(:user)
    end

    policy action_type([:update, :destroy]) do
      authorize_if relates_to_actor_via(:user)
    end
  end

  attributes do
    uuid_primary_key :id
    attribute :user_id, :uuid, allow_nil?: false
    attribute :title, :string, allow_nil?: false
  end

  relationships do
    has_many :sections, L3rnDev.Books.Section

    belongs_to :user, L3rnDev.Accounts.User do
      allow_nil? false
    end
  end
end
