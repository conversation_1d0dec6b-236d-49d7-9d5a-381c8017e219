defmodule L3rnDev.Secrets do
  use AshAuthentication.Secret

  def secret_for(
        [:authentication, :tokens, :signing_secret],
        L3rnDev.Accounts.User,
        _opts,
        _context
      ) do
    Application.fetch_env(:l3rn_dev, :token_signing_secret)
  end

  def secret_for([:authentication, :strategies, :github, :client_id], L3rnDev.Accounts.User, _, _) do
    get_config(:client_id)
  end

  def secret_for(
        [:authentication, :strategies, :github, :redirect_uri],
        L3rnDev.Accounts.User,
        _,
        _
      ) do
    get_config(:redirect_uri)
  end

  def secret_for(
        [:authentication, :strategies, :github, :client_secret],
        L3rnDev.Accounts.User,
        _,
        _
      ) do
    get_config(:client_secret)
  end

  defp get_config(key) do
    :l3rn_dev
    |> Application.get_env(:github, [])
    |> Keyword.fetch(key)
  end
end
