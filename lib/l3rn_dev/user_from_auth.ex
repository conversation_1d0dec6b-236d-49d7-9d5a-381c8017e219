defmodule L3rnDev.UserFromAuth do
  @moduledoc """
  Retrieve the user information from an auth request
  """
  require Logger
  require <PERSON>

  alias L3rnDev.Accounts.User
  alias <PERSON><PERSON><PERSON><PERSON>.Auth
  use L3rnDevWeb, :verified_routes

  def find_or_create(%Auth{provider: :github} = auth) do
    result =
      auth
      |> uid_from_auth()
      |> User.get_by_github_uid()

    case result do
      {:ok, user} ->
        # Refresh the user's token

        user
        |> Ash.Changeset.for_update(:update_tokens, %{
          github_token: github_token_from_auth(auth),
          github_token_expires_at: NaiveDateTime.utc_now() |> NaiveDateTime.add(28_800, :second),
          github_refresh_token: github_refresh_token_from_auth(auth),
          github_refresh_token_expires_at:
            NaiveDateTime.utc_now() |> NaiveDateTime.add(15_897_600, :second)
        })
        |> Ash.update!()

        {:ok, user}

      {:error, _} ->
        local_avatar_uri =
          auth
          |> get_avatar()
          |> upload_avatar(auth.info.nickname)

        register_result =
          auth
          |> user_from_github_auth(local_avatar_uri)
          |> register_from_github()

        {:ok, register_result}
    end
  end

  def find_or_create(%Auth{} = auth) do
    {:ok, basic_info(auth)}
  end

  defp register_from_github(params) do
    L3rnDev.Accounts.User
    |> Ash.Changeset.for_create(:register_from_github, params)
    |> Ash.create!()
  end

  defp upload_avatar({:ok, %Tesla.Env{status: 200, body: body} = env}, login) do
    bucket_name = Application.get_env(:l3rn_dev, :bucket_name)
    bucket_folder = Application.get_env(:l3rn_dev, :bucket_folder)

    if bucket_name != nil do
      file_uuid = UUID.uuid4(:hex)
      unique_filename = "#{file_uuid}.#{get_file_extension(env)}"

      props = [
        {:cache_control, "must-revalidate"},
        {:acl, :public_read}
      ]

      ExAws.S3.put_object(bucket_name, "#{bucket_folder}/#{unique_filename}", body, props)
      |> ExAws.request!()

      "https://#{bucket_name}.nyc3.cdn.digitaloceanspaces.com/#{bucket_folder}/#{unique_filename}"
    else
      dest =
        Path.join([
          :code.priv_dir(:l3rn_dev),
          "static",
          "avatars"
        ])

      file_name = login <> "." <> get_file_extension(env)
      dest |> Path.join(login) |> File.mkdir_p()
      dest |> Path.join(file_name) |> File.write!(body)
      ~p"/avatars/#{file_name}"
    end
  end

  defp upload_avatar({:error, _error}, _login), do: nil
  defp upload_avatar({:ok, _other}, _login), do: nil

  defp get_avatar(auth) do
    with url <- avatar_from_auth(auth),
         {:ok, %Tesla.Env{} = env} <- Tesla.get(url) do
      {:ok, env}
    else
      {:error, _err} ->
        auth.info
        |> Map.get(:email)
        |> get_gravatar()
    end
  end

  defp get_gravatar(nil), do: {:error, nil}

  defp get_gravatar(email) do
    hash =
      email
      |> String.trim()
      |> String.downcase()
      |> :erlang.md5()
      |> Base.encode16(case: :lower)

    Logger.info(
      "Getting Gravatar from: https://www.gravatar.com/avatar/#{hash}?s=150&d=identicon"
    )

    Tesla.get("https://www.gravatar.com/avatar/#{hash}?s=150&d=identicon")
  end

  defp get_file_extension(%Tesla.Env{} = env) do
    type = Tesla.get_header(env, "content-type")

    case type do
      "image/jpg" -> "jpg"
      "image/jpeg" -> "jpg"
      "image/png" -> "png"
      _ -> "jpg"
    end
  end

  # github does it this way
  defp avatar_from_auth(%{info: %{urls: %{avatar_url: image}}}), do: image

  # facebook does it this way
  defp avatar_from_auth(%{info: %{image: image}}), do: image

  # default case if nothing matches
  defp avatar_from_auth(auth) do
    Logger.warning("#{auth.provider} needs to find an avatar URL!")
    Logger.debug(Jason.encode!(auth))
    nil
  end

  defp email_from_auth(%{info: %{email: email}}), do: email

  defp uid_from_auth(auth), do: Kernel.inspect(auth.uid)

  defp github_nickname_from_auth(auth), do: auth.info.nickname

  defp github_token_from_auth(auth), do: auth.credentials.token

  defp github_refresh_token_from_auth(auth), do: auth.credentials.refresh_token

  defp basic_info(auth) do
    %{
      id: auth.uid,
      email: email_from_auth(auth),
      name: name_from_auth(auth),
      avatar: avatar_from_auth(auth)
    }
  end

  defp name_from_auth(auth) do
    if auth.info.name do
      auth.info.name
    else
      name =
        [auth.info.first_name, auth.info.last_name]
        |> Enum.filter(&(&1 != nil and &1 != ""))

      if Enum.empty?(name) do
        auth.info.nickname
      else
        Enum.join(name, " ")
      end
    end
  end

  defp user_from_github_auth(auth, local_avatar_uri),
    do: %{
      github_uid: uid_from_auth(auth),
      email: Map.get(auth.info, :email),
      avatar: local_avatar_uri,
      github_nickname: github_nickname_from_auth(auth),
      github_token: github_token_from_auth(auth),
      github_token_expires_at: NaiveDateTime.utc_now() |> NaiveDateTime.add(28_800, :second),
      github_refresh_token: github_refresh_token_from_auth(auth),
      github_refresh_token_expires_at:
        NaiveDateTime.utc_now() |> NaiveDateTime.add(15_897_600, :second)
    }
end
