defmodule L3rnDev.Accounts.User do
  use Ash.Resource,
    data_layer: AshPostgres.DataLayer,
    authorizers: [Ash.Policy.Authorizer],
    domain: L3rnDev.Accounts,
    extensions: [AshAuthentication]

  postgres do
    table "users"
    repo L3rnDev.Repo
  end

  @moduledoc false

  authentication do
    add_ons do
      log_out_everywhere do
        apply_on_password_change? true
      end
    end

    tokens do
      enabled? true
      token_resource L3rnDev.Accounts.Token
      signing_secret L3rnDev.Secrets
      store_all_tokens? true
      require_token_presence_for_authentication? true
    end

    strategies do
      magic_link do
        identity_field :email
        registration_enabled? true
        require_interaction? true

        sender L3rnDev.Accounts.User.Senders.SendMagicLinkEmail
      end

      api_key :api_key do
        api_key_relationship :valid_api_keys
        api_key_hash_attribute :api_key_hash
      end
    end
  end

  code_interface do
    define :read_all, action: :read
    define :get_by_id, args: [:id], action: :by_id
    define :get_by_github_uid, args: [:uid], action: :by_github_uid
  end

  actions do
    defaults []

    create :register_from_github do
      accept [
        :github_uid,
        :email,
        :avatar,
        :github_nickname,
        :github_token,
        :github_token_expires_at,
        :github_refresh_token,
        :github_refresh_token_expires_at
      ]
    end

    read :read do
      primary? true
      pagination offset?: true, default_limit: 20, countable: true, required?: false
    end

    # Defines custom read action which fetches post by id.
    read :by_id do
      argument :id, :uuid, allow_nil?: false
      # Tells us we expect this action to return a single result
      get? true
      # Filters the `:id` given in the argument
      # against the `id` of each element in the resource
      filter expr(id == ^arg(:id))
    end

    read :by_github_uid do
      argument :uid, :string, allow_nil?: false

      get? true

      filter expr(github_uid == ^arg(:uid))
    end

    update :update_tokens do
      accept [
        :github_token,
        :github_token_expires_at,
        :github_refresh_token,
        :github_refresh_token_expires_at
      ]

      require_atomic? false
    end

    action :impersonate, :atom do
      run fn input, _ ->
        :ok
      end
    end

    read :get_by_subject do
      description "Get a user by the subject claim in a JWT"
      argument :subject, :string, allow_nil?: false
      get? true
      prepare AshAuthentication.Preparations.FilterBySubject
    end

    read :get_by_email do
      description "Looks up a user by their email"
      get? true

      argument :email, :ci_string do
        allow_nil? false
      end

      filter expr(email == ^arg(:email))
    end

    create :sign_in_with_magic_link do
      description "Sign in or register a user with magic link."

      argument :token, :string do
        description "The token from the magic link that was sent to the user"
        allow_nil? false
      end

      upsert? true
      upsert_identity :unique_email
      upsert_fields [:email]

      # Uses the information from the token to create or sign in the user
      change AshAuthentication.Strategy.MagicLink.SignInChange

      metadata :token, :string do
        allow_nil? false
      end
    end

    action :request_magic_link do
      argument :email, :ci_string do
        allow_nil? false
      end

      run AshAuthentication.Strategy.MagicLink.Request
    end

    read :sign_in_with_api_key do
      argument :api_key, :string, allow_nil?: false
      prepare AshAuthentication.Strategy.ApiKey.SignInPreparation
    end
  end

  policies do
    bypass AshAuthentication.Checks.AshAuthenticationInteraction do
      authorize_if always()
    end

    policy always() do
      authorize_if always()
    end

    policy action(:impersonate) do
      authorize_if actor_attribute_equals(:admin, true)
      forbid_if always()
    end
  end

  validations do
    validate match(:email, ~r/\A[^@\s]+@[^@\s]+\z/), message: "is invalid"
  end

  attributes do
    uuid_primary_key :id
    attribute :email, :ci_string, allow_nil?: false, public?: true
    attribute :github_uid, :string
    attribute :avatar, :string
    attribute :github_nickname, :string
    attribute :github_token, :string
    attribute :github_token_expires_at, :naive_datetime
    attribute :github_refresh_token, :string
    attribute :github_refresh_token_expires_at, :naive_datetime
  end

  def refresh_github_tokens(user) do
    L3rnDev.Github.Api.refresh_token(user)
    |> case do
      {:ok,
       %{
         "access_token" => access_token,
         "expires_in" => expires_in,
         "refresh_token" => refresh_token,
         "refresh_token_expires_in" => refresh_token_expires_in
       }, _} ->
        user
        |> Ash.Changeset.for_update(:update_tokens, %{
          github_token: access_token,
          github_token_expires_at:
            NaiveDateTime.utc_now() |> NaiveDateTime.add(expires_in, :second),
          github_refresh_token: refresh_token,
          github_refresh_token_expires_at:
            NaiveDateTime.utc_now() |> NaiveDateTime.add(refresh_token_expires_in, :second)
        })
        |> Ash.update!()

      {:ok, %{"error" => "bad_refresh_token"}, _} ->
        user
        |> Ash.Changeset.for_update(:update_tokens, %{
          github_token: "",
          github_token_expires_at: NaiveDateTime.utc_now(),
          github_refresh_token: "",
          github_refresh_token_expires_at: NaiveDateTime.utc_now()
        })
        |> Ash.update!()

      {:error, _} ->
        user
    end
  end

  relationships do
    has_many :valid_api_keys, L3rnDev.Accounts.ApiKey do
      filter expr(valid)
    end
  end

  identities do
    identity :unique_email, [:email]
  end
end
