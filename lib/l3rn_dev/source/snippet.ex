defmodule L3rnDev.Source.Snippet do
  # This turns this module into a resource
  use Ash.Resource,
    domain: L3rnDev.Source,
    data_layer: AshPostgres.DataLayer,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [AshPhoenix]

  postgres do
    table "source_snippets"
    repo L3rnDev.Repo
  end

  forms do
    form :create, args: [:name]
  end

  code_interface do
    define :create, action: :create
    define :get_by_id, args: [:id], action: :by_id
  end

  actions do
    defaults [:read]

    create :create do
      accept [:name, :visibility]

      change relate_actor(:user)
    end

    read :by_id do
      argument :id, :uuid, allow_nil?: false

      get? true

      filter expr(id == ^arg(:id))
    end
  end

  policies do
    policy action_type(:create) do
      authorize_if actor_present()
    end

    policy action_type(:read) do
      authorize_if relates_to_actor_via(:user)
    end

    policy action_type([:update, :destroy]) do
      authorize_if relates_to_actor_via(:user)
    end
  end

  # Attributes are the simple pieces of data that exist on your resource
  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid, allow_nil?: false
    attribute :name, :string, allow_nil?: false

    attribute :visibility, :atom do
      constraints one_of: [:public, :private]

      default :private

      allow_nil? false
    end

    create_timestamp :inserted_at
    update_timestamp :updated_at
  end

  relationships do
    has_many :files, L3rnDev.Source.File

    belongs_to :user, L3rnDev.Accounts.User do
      allow_nil? false
    end
  end

  identities do
    identity :unique_name, [:name, :user_id]
  end
end
