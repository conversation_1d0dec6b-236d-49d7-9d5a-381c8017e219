defmodule L3rnDev.Source.Line do
  # This turns this module into a resource
  use Ash.Resource,
    domain: L3rnDev.Source,
    authorizers: [Ash.Policy.Authorizer],
    data_layer: AshPostgres.DataLayer

  postgres do
    table "source_lines"
    repo L3rnDev.Repo
  end

  actions do
    # Use the default implementation of the :read action
    defaults [:read]

    # and a create action, which we'll customize later
    create :create

    read :lines_by_file do
      argument :file_id, :uuid do
        allow_nil? false
      end

      filter expr(file_id == ^arg(:file_id))
    end
  end

  policies do
    policy action_type(:read) do
      authorize_if relates_to_actor_via([:file, :snippet, :user])
    end

    policy action_type([:update, :destroy]) do
      authorize_if relates_to_actor_via([:file, :snippet, :user])
    end
  end

  # Attributes are the simple pieces of data that exist on your resource
  attributes do
    # Add an autogenerated UUID primary key called `:id`.
    uuid_primary_key :id

    attribute :type, :string, allow_nil?: false
    attribute :line_from, :integer, allow_nil?: false
    attribute :line_to, :integer, allow_nil?: false
    attribute :text, :string, allow_nil?: false

    create_timestamp :inserted_at
    update_timestamp :updated_at
  end

  relationships do
    belongs_to :file, L3rnDev.Source.File do
      allow_nil? false
    end
  end
end
