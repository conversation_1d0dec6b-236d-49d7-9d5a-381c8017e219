defmodule L3rnDev.Source.File do
  # This turns this module into a resource
  use Ash.Resource,
    domain: L3rnDev.Source,
    authorizers: [Ash.Policy.Authorizer],
    data_layer: AshPostgres.DataLayer

  postgres do
    table "source_files"
    repo L3rnDev.Repo
  end

  actions do
    # Use the default implementation of the :read action
    defaults [:read]

    # and a create action, which we'll customize later
    create :create

    read :files_by_snippet do
      argument :snippet_id, :uuid do
        allow_nil? false
      end

      filter expr(snippet_id == ^arg(:snippet_id))
    end
  end

  policies do
    policy action_type(:read) do
      authorize_if relates_to_actor_via([:snippet, :user])
    end

    policy action_type([:update, :destroy]) do
      authorize_if relates_to_actor_via([:snippet, :user])
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :url, :string, allow_nil?: false
    attribute :sha, :string, allow_nil?: false
    attribute :owner, :string, allow_nil?: false
    attribute :repository_id, :string, allow_nil?: false
    attribute :filename, :string, allow_nil?: false
    create_timestamp :inserted_at
    update_timestamp :updated_at
  end

  relationships do
    belongs_to :snippet, L3rnDev.Source.Snippet do
      allow_nil? false
    end

    has_many :lines, L3rnDev.Source.Line
  end
end
