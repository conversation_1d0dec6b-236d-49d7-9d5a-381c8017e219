defmodule L3rnDev.Github.RepositoryImportWorker do
  require <PERSON><PERSON>
  use Oban.Worker
  import Ash.Expr

  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"user_id" => user_id} = _args}) do
    start_time = DateTime.utc_now()
    user = L3rnDev.Accounts.User.get_by_id!(user_id)
    {:ok, repositories, links} = L3rnDev.Github.Api.get_repositories(user)
    step(start_time, user, repositories, links)
    :ok
  end

  defp step(start_time, user, repositories, {_, nil}) do
    process_repositories(user, repositories)
    update_archived_repositories(user.id, start_time)
  end

  defp step(start_time, user, repositories, {_, next}) do
    process_repositories(user, repositories)
    {:ok, repositories, links} = L3rnDev.Github.Api.get_repositories(user, next)
    step(start_time, user, repositories, links)
  end

  defp update_archived_repositories(user_id, start_time) do
    Ash.bulk_update!(
      L3rnDev.Github.Repository,
      :update_archived,
      %{archived: true},
      filter: expr(user_id == ^user_id and last_sync_at < ^start_time)
    )
  end

  defp process_repositories(user, repositories) do
    Enum.each(repositories, fn repo ->
      owner =
        L3rnDev.Github.User
        |> Ash.Changeset.for_create(:create_user, %{
          user_id: user.id,
          login: repo["owner"]["login"]
        })
        |> Ash.create!()

      L3rnDev.Github.Repository
      |> Ash.Changeset.for_create(:create_repository, %{
        owner_id: owner.id,
        user_id: user.id,
        name: repo["name"],
        full_name: repo["full_name"],
        description: repo["description"],
        visibility: repo["visibility"],
        last_sync_at: DateTime.utc_now()
      })
      |> Ash.create!()
    end)
  end
end
