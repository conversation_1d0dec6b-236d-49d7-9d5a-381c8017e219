defmodule L3rnDev.Github.Commit do
  use Ash.Resource, domain: L3rnDev.Github, data_layer: AshPostgres.DataLayer

  postgres do
    table "github_commits"
    repo L3rnDev.Repo
  end

  actions do
    # Use the default implementation of the :read action
    defaults [:read]

    # and a create action, which we'll customize later
    create :create
  end

  # Attributes are the simple pieces of data that exist on your resource
  attributes do
    # Add an autogenerated UUID primary key called `:id`.
    uuid_primary_key :id

    # Add a string type attribute called `:subject`
    attribute :sha, :string
  end

  # Relationships are the associations between resources
  relationships do
    # Add a belongs_to relationship called `:repository` that references the `L3rnDev.Github.Repository` resource
    belongs_to :repository, L3rnDev.Github.Repository
  end
end
