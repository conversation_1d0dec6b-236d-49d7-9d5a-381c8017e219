defmodule L3rnDev.Github.Repository do
  use Ash.Resource, domain: L3rnDev.Github, data_layer: AshPostgres.DataLayer

  postgres do
    table "github_repositories"
    repo L3rnDev.Repo
  end

  code_interface do
    define :get_by_user_id, args: [:user_id], action: :by_user_id
  end

  actions do
    # Use the default implementation of the :read action
    defaults [:read]

    # and a create action, which we'll customize later
    create :create_repository do
      accept [:name, :full_name, :description, :owner_id, :user_id, :visibility, :last_sync_at]
      upsert? true
      upsert_identity :unique_name
    end

    read :by_user_id do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))

      pagination do
        required? false
        keyset? true
        countable true
      end
    end

    update :update_archived do
      accept [:archived]
    end
  end

  # Attributes are the simple pieces of data that exist on your resource
  attributes do
    uuid_primary_key :id

    attribute :name, :string, allow_nil?: false

    attribute :full_name, :string, allow_nil?: false, public?: true

    attribute :description, :string, public?: true

    attribute :owner_id, :uuid, allow_nil?: false

    attribute :visibility, :atom do
      constraints one_of: [:public, :private]

      default :public

      allow_nil? false
    end

    attribute :user_id, :uuid, allow_nil?: false

    attribute :last_sync_at, :utc_datetime, allow_nil?: false, default: &DateTime.utc_now/0

    attribute :archived, :boolean, allow_nil?: false, default: false
  end

  # Relationships are the associations between resources
  relationships do
    # Add a belongs_to relationship called `:owner` that references the `L3rnDev.Github.User` resource
    belongs_to :owner, L3rnDev.Github.User
  end

  identities do
    identity :unique_name, [:name, :owner_id, :user_id]
  end
end
