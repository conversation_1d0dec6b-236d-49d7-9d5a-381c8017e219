defmodule L3rnDev.Github.User do
  use Ash.Resource, domain: L3rnDev.Github, data_layer: AshPostgres.DataLayer

  postgres do
    table "github_users"
    repo L3rnDev.Repo
  end

  actions do
    # Use the default implementation of the :read action
    defaults [:read]

    # and a create action, which we'll customize later
    create :create_user do
      accept [:login, :user_id]
      upsert? true
      upsert_identity :unique_login
    end
  end

  # Attributes are the simple pieces of data that exist on your resource
  attributes do
    uuid_primary_key :id

    # Add a string type attribute called `:login`
    attribute :login, :string

    attribute :user_id, :uuid, allow_nil?: true
  end

  # Relationships are the associations between resources
  relationships do
    # Add a has_many relationship called `:repositories` that references the `L3rnDev.Github.Repository` resource
    has_many :repositories, L3rnDev.Github.Repository, destination_attribute: :owner_id
  end

  identities do
    identity :unique_login, [:login, :user_id]
  end
end
