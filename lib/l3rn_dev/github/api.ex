defmodule L3rnDev.Github.Api do
  @moduledoc """
  Github API client.
  """

  require Logger

  def get_commit_diff(user, owner, repo, sha) do
    get_api!(user)
    |> Req.get!(
      url: "/repos/#{owner}/#{repo}/commits/#{sha}",
      headers: [
        {"Accept", "application/vnd.github.diff"}
      ]
    )
    |> call_or_refresh(user, fn -> get_commit_diff(user, owner, repo, sha) end)
  end

  def get_commit(user, owner, repo, sha) do
    get_api!(user)
    |> Req.get!(url: "/repos/#{owner}/#{repo}/commits/#{sha}")
    |> call_or_refresh(user, fn -> get_commit(user, owner, repo, sha) end)
  end

  def get_commits(user, owner, repo, url \\ nil, per_page \\ 30, page \\ 1) do
    get_api!(user)
    |> Req.get!(url: url || "/repos/#{owner}/#{repo}/commits?page=#{page}&per_page=#{per_page}")
    |> call_or_refresh(user, fn -> get_commits(user, owner, repo, url, per_page, page) end)
  end

  def get_repository(user, owner, repo) do
    get_api!(user)
    |> Req.get!(url: "/repos/#{owner}/#{repo}")
    |> call_or_refresh(user, fn -> get_repository(user, owner, repo) end)
  end

  def get_repositories(user, url \\ nil, per_page \\ 30, page \\ 1) do
    get_api!(user)
    |> Req.get!(url: url || "/user/repos?page=#{page}&per_page=#{per_page}")
    |> call_or_refresh(user, fn -> get_repositories(user, url, per_page, page) end)
  end

  def get_org(user, name) do
    get_api!(user)
    |> Req.get!(
      url: "/orgs/#{name}",
      auth: {:bearer, user.github_token},
      user_agent: "l3rn-dev"
    )
    |> call_or_refresh(user, fn -> get_org(user, name) end)
  end

  def get_orgs(user, url \\ nil, per_page \\ 30, page \\ 1) do
    get_api!(user)
    |> Req.get!(url: url || "/user/orgs?page=#{page}&per_page=#{per_page}")
    |> call_or_refresh(user, fn -> get_orgs(user, url, per_page, page) end)
  end

  def org?(user, name) do
    case get_org(user, name) do
      {:error, :not_found} -> false
      _ -> true
    end
  end

  def refresh_token(user) do
    req = get_api!(user, "https://github.com")

    res =
      Req.post!(req,
        url:
          "/login/oauth/access_token?client_id=#{System.get_env("GITHUB_CLIENT_ID")}&client_secret=#{System.get_env("GITHUB_CLIENT_SECRET")}&refresh_token=#{user.github_refresh_token}&grant_type=refresh_token",
        headers: [
          {"Accept", "application/json"}
        ]
      )

    check_api_response(res)
  end

  defp call_or_refresh(res, user, call) do
    case check_api_response(res) do
      {:error, :unauthorized} ->
        case L3rnDev.Accounts.User.refresh_github_tokens(user) do
          {:ok, _} -> call.()
          _ -> check_api_response(res)
        end

      _ ->
        check_api_response(res)
    end
  end

  defp remove_host(nil), do: nil
  defp remove_host(url), do: String.replace(url, "https://api.github.com", "")

  defp get_links(nil), do: {nil, nil}

  defp get_links(link) do
    {:ok, parsed_links} = ExHttpLink.parse(List.first(link))

    {
      remove_host(
        Enum.find_value(parsed_links, fn {link, {_, rel}} -> rel == "prev" && link end)
      ),
      remove_host(Enum.find_value(parsed_links, fn {link, {_, rel}} -> rel == "next" && link end))
    }
  end

  defp check_api_response(res) do
    links =
      if res.status != 200 and res.status != 201 do
        Logger.info("#{res.status}: #{res.body["message"]}")
        nil
      else
        get_links(res.headers["link"])
      end

    case res.status do
      200 -> {:ok, res.body, links}
      201 -> {:ok, res.body, links}
      404 -> {:error, :not_found}
      # see https://docs.github.com/en/rest/overview/resources-in-the-rest-api?apiVersion=2022-11-28#failed-login-limit
      401 -> {:error, :unauthorized}
      403 -> {:error, :forbidden}
      _ -> {:error, :unknown}
    end
  end

  defp get_api!(user, base_url \\ "https://api.github.com") do
    req = Req.new(base_url: base_url, user_agent: "l3rn-dev")
    if user.github_token, do: Req.merge(req, auth: {:bearer, user.github_token}), else: req
  end
end
