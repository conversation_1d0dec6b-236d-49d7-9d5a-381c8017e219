# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Elixir CI

on:
  push:
    branches: ["develop"]
  pull_request:
    branches: ["develop"]

permissions:
  contents: read

jobs:
  container-job:
    runs-on: self-hosted
    services:
      # Label used to access the service container
      postgres:
        # Docker Hub image
        image: postgres
        # Provide the password for postgres
        env:
          POSTGRES_USER: root
          POSTGRES_PASSWORD: postgres
          POSTGRES_PORT: 5432
        ports:
          - 6543:5432
        # Set health checks to wait until postgres has started
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    env:
      ImageOS: ubuntu20
      POSTGRES_USER: root
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST: localhost
      POSTGRES_PORT: 6543
    steps:
      - uses: actions/checkout@v3
      - name: Set up <PERSON><PERSON><PERSON>
        uses: erlef/setup-beam@v1.17
        with:
          elixir-version: "1.18" # Define the elixir version [required]
          otp-version: "27" # Define the OTP version [required]
      - name: Restore dependencies cache
        uses: actions/cache@v3
        with:
          path: deps
          key: ${{ runner.os }}-mix-${{ hashFiles('**/mix.lock') }}
          restore-keys: ${{ runner.os }}-mix-
      - name: Install dependencies
        run: mix deps.get
      - name: Run tests
        run: mix test
